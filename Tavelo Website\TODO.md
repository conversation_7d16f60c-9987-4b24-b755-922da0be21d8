# Next.js Conversion Plan for Tavelo Website

## Overview
Convert the static HTML Tavelo travel booking website to a Next.js React application using Tailwind CSS instead of Bootstrap.

## Steps
- [ ] Set up Next.js project with Tailwind CSS in `nextjs/` subfolder
- [ ] Copy static assets (images, icons, fonts) to `nextjs/public/assets/`
- [ ] Create reusable layout components (Header, Footer, Navigation)
- [ ] Convert `index.html` to `pages/index.tsx`
- [ ] Convert `about.html` to `pages/about.tsx`
- [ ] Convert `contact.html` to `pages/contact.tsx`
- [ ] Convert `gallery.html` to `pages/gallery.tsx`
- [ ] Convert `blog.html` to `pages/blog.tsx`
- [ ] Convert `blog-single.html` to `pages/blog/[slug].tsx`
- [ ] Convert `destination.html` to `pages/destination.tsx`
- [ ] Convert `team.html` to `pages/team.tsx`
- [ ] Convert `testimonial.html` to `pages/testimonial.tsx`
- [ ] Convert `service.html` to `pages/service.tsx`
- [ ] Convert `service-single.html` to `pages/service/[slug].tsx`
- [ ] Convert `faq.html` to `pages/faq.tsx`
- [ ] Convert `pricing.html` to `pages/pricing.tsx`
- [ ] Convert `terms.html` to `pages/terms.tsx`
- [ ] Convert `privacy.html` to `pages/privacy.tsx`
- [ ] Convert `404.html` to `pages/404.tsx`
- [ ] Convert `coming-soon.html` to `pages/coming-soon.tsx`
- [ ] Convert login/register pages to `pages/auth/login.tsx`, `pages/auth/register.tsx`, etc.
- [ ] Convert dashboard and profile pages to `pages/dashboard.tsx`, `pages/profile.tsx`, etc.
- [ ] Convert booking-related pages (flight, hotel, tour, etc.) to respective routes
- [ ] Update all internal links to use Next.js Link component
- [ ] Replace Bootstrap classes with Tailwind CSS utilities
- [ ] Migrate JavaScript functionality to React hooks and components
- [ ] Implement responsive design with Tailwind
- [ ] Test all pages and functionality
- [ ] Optimize images and assets
- [ ] Configure deployment settings

## Notes
- Use TypeScript for type safety
- Implement proper SEO with Next.js Head component
- Ensure accessibility and performance
- Handle forms and interactions with React state
