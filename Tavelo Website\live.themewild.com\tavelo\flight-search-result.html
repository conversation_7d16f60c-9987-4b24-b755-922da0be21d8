<!DOCTYPE html>
<html lang="en">


<!-- Mirrored from live.themewild.com/tavelo/flight-search-result.html by HTTrack Website Copier/3.x [XR&CO'2014], Tue, 02 Sep 2025 05:11:29 GMT -->
<head>
    <!-- meta tags -->
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="">
    <meta name="keywords" content="">

    <!-- title -->
    <title>Tavelo - Travel Booking HTML5 Template</title>

    <!-- favicon -->
    <link rel="icon" type="image/x-icon" href="assets/img/logo/favicon.png">

    <!-- css -->
    <link rel="stylesheet" href="assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="assets/css/all-fontawesome.min.css">
    <link rel="stylesheet" href="assets/css/animate.min.css">
    <link rel="stylesheet" href="assets/css/magnific-popup.min.css">
    <link rel="stylesheet" href="assets/css/owl.carousel.min.css">
    <link rel="stylesheet" href="assets/css/nice-select.min.css">
    <link rel="stylesheet" href="assets/css/jquery-ui.min.css">
    <link rel="stylesheet" href="assets/css/jquery.timepicker.min.css">
    <link rel="stylesheet" href="assets/css/style.css">

</head>

<body>

    <!-- preloader -->
    <div class="preloader">
        <div class="loader">
            <span style="--i:1;"></span>
            <span style="--i:2;"></span>
            <span style="--i:3;"></span>
            <span style="--i:4;"></span>
            <span style="--i:5;"></span>
            <span style="--i:6;"></span>
            <span style="--i:7;"></span>
            <span style="--i:8;"></span>
            <span style="--i:9;"></span>
            <span style="--i:10;"></span>
            <span style="--i:11;"></span>
            <span style="--i:12;"></span>
            <span style="--i:13;"></span>
            <span style="--i:14;"></span>
            <span style="--i:15;"></span>
            <span style="--i:16;"></span>
            <span style="--i:17;"></span>
            <span style="--i:18;"></span>
            <span style="--i:19;"></span>
            <span style="--i:20;"></span>
            <div class="loader-plane"></div>
        </div>
    </div>
    <!-- preloader end -->


    <!-- header area -->
    <header class="header">

        <!-- header-top -->
        <div class="header-top">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-7">
                        <div class="header-top-left">
                            <div class="top-social">
                                <a href="#"><i class="fab fa-facebook-f"></i></a>
                                <a href="#"><i class="fab fa-x-twitter"></i></a>
                                <a href="#"><i class="fab fa-instagram"></i></a>
                                <a href="#"><i class="fab fa-linkedin-in"></i></a>
                            </div>
                            <div class="top-contact-info">
                                <ul>
                                    <li><a href="tel:+21234567897"><i class="far fa-phone-arrow-down-left"></i>******
                                            4567 897</a></li>
                                    <li><a href="https://live.themewild.com/cdn-cgi/l/email-protection#137a7d757c53766b727e637f763d707c7e"><i
                                                class="far fa-envelopes"></i><span class="__cf_email__" data-cfemail="147d7a727b54716c75796478713a777b79">[email&#160;protected]</span></a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-5">
                        <div class="header-top-right">
                            <div class="lang">
                                <select name="lang" class="select">
                                    <option value="1">ENG</option>
                                    <option value="2">RUS</option>
                                    <option value="3">ROM</option>
                                    <option value="4">FRA</option>
                                    <option value="5">ESP</option>
                                    <option value="6">POR</option>
                                </select>
                            </div>
                            <div class="currency">
                                <select name="currency" class="select">
                                    <option value="1">USD</option>
                                    <option value="2">EUR</option>
                                    <option value="3">AUD</option>
                                    <option value="4">BRL</option>
                                    <option value="5">CAD</option>
                                    <option value="6">MXN</option>
                                </select>
                            </div>
                            <div class="account">
                                <a href="login.html"><i class="far fa-sign-in"></i>Login</a>
                                <a href="register.html"><i class="far fa-user-tie"></i>Sign Up</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- header-top end -->

        <!-- navbar -->
        <div class="main-navigation">
            <nav class="navbar navbar-expand-lg">
                <div class="container">
                    <a class="navbar-brand" href="index-2.html">
                        <img src="assets/img/logo/logo.png" class="logo-display" alt="logo">
                        <img src="assets/img/logo/logo-dark.png" class="logo-scrolled" alt="logo">
                    </a>
                    <div class="mobile-menu-right">
                        <button class="navbar-toggler" type="button" data-bs-toggle="collapse"
                            data-bs-target="#main_nav" aria-expanded="false" aria-label="Toggle navigation">
                            <span class="navbar-toggler-btn-icon"><i class="far fa-bars"></i></span>
                        </button>
                    </div>
                    <div class="collapse navbar-collapse" id="main_nav">
                        <ul class="navbar-nav">
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle active" href="#" data-bs-toggle="dropdown">Home</a>
                                <ul class="dropdown-menu fade-down">
                                    <li><a class="dropdown-item" href="index-2.html">Home One</a></li>
                                    <li><a class="dropdown-item" href="index-3.html">Home Two</a></li>
                                    <li><a class="dropdown-item" href="index-4.html">Home Three</a></li>
                                    <li><a class="dropdown-item" href="index-5.html">Home Flight</a></li>
                                    <li><a class="dropdown-item" href="index-6.html">Home Hotel</a></li>
                                    <li><a class="dropdown-item" href="index-7.html">Home Activity</a></li>
                                    <li><a class="dropdown-item" href="index-8.html">Home Car</a></li>
                                    <li><a class="dropdown-item" href="index-9.html">Home Cruise</a></li>
                                    <li><a class="dropdown-item" href="index-10.html">Home Tour</a></li>
                                </ul>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown">Flight</a>
                                <ul class="dropdown-menu fade-down">
                                    <li><a class="dropdown-item" href="flight-grid.html">Flight Grid</a></li>
                                    <li><a class="dropdown-item" href="flight-list.html">Flight List</a></li>
                                    <li><a class="dropdown-item" href="flight-full-width.html">Flight Full Width</a>
                                    </li>
                                    <li><a class="dropdown-item" href="flight-single.html">Flight Details</a></li>
                                    <li><a class="dropdown-item" href="flight-booking.html">Flight Booking</a></li>
                                    <li><a class="dropdown-item" href="flight-search-result.html">Flight Search
                                            Result</a></li>
                                    <li><a class="dropdown-item" href="flight-add.html">Add Flight</a></li>
                                </ul>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown">Hotel</a>
                                <ul class="dropdown-menu fade-down">
                                    <li><a class="dropdown-item" href="hotel-grid.html">Hotel Grid</a></li>
                                    <li><a class="dropdown-item" href="hotel-list.html">Hotel List</a></li>
                                    <li><a class="dropdown-item" href="hotel-full-width.html">Hotel Full Width</a></li>
                                    <li class="dropdown-submenu">
                                        <a class="dropdown-item dropdown-toggle" href="#">Hotel Room</a>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="hotel-room-grid.html">Room Grid</a></li>
                                            <li><a class="dropdown-item" href="hotel-room-list.html">Room List</a></li>
                                            <li><a class="dropdown-item" href="hotel-room-full-width.html">Room Full
                                                    Width</a></li>
                                            <li><a class="dropdown-item" href="hotel-room-single.html">Room Details</a>
                                            </li>
                                            <li><a class="dropdown-item" href="hotel-room-search-result.html">Room
                                                    Search Result</a></li>
                                            <li><a class="dropdown-item" href="hotel-room-add.html">Add Room</a></li>
                                        </ul>
                                    </li>
                                    <li><a class="dropdown-item" href="hotel-single.html">Hotel Details</a></li>
                                    <li><a class="dropdown-item" href="hotel-booking.html">Hotel Booking</a></li>
                                    <li><a class="dropdown-item" href="hotel-search-result.html">Hotel Search Result</a>
                                    </li>
                                    <li><a class="dropdown-item" href="hotel-add.html">Add Hotel</a></li>
                                </ul>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown">Pages</a>
                                <div class="dropdown-menu mega-menu fade-down">
                                    <div class="row">
                                        <div class="col-lg-3">
                                            <ul>
                                                <li><a class="dropdown-item" href="destination.html">Destination</a>
                                                </li>
                                                <li><a class="dropdown-item" href="career.html">Career</a></li>
                                                <li><a class="dropdown-item" href="career-single.html">Career
                                                        Details</a></li>
                                                <li><a class="dropdown-item" href="become-expert.html">Become An
                                                        Expert</a></li>
                                                <li><a class="dropdown-item" href="cart.html">My Cart</a></li>
                                                <li><a class="dropdown-item" href="checkout.html">Checkout</a></li>
                                                <li><a class="dropdown-item" href="booking-confirm.html">Booking
                                                        Confirm</a></li>
                                                <li><a class="dropdown-item" href="login.html">Login</a></li>
                                                <li><a class="dropdown-item" href="register.html">Sign Up</a></li>
                                            </ul>
                                        </div>
                                        <div class="col-lg-3">
                                            <ul>
                                                <li><a class="dropdown-item" href="forgot-password.html">Forgot
                                                        Password</a></li>
                                                <li><a class="dropdown-item" href="dashboard.html">Dashboard</a></li>
                                                <li><a class="dropdown-item" href="profile.html">My Profile</a></li>
                                                <li><a class="dropdown-item" href="profile-listing.html">My Listing</a>
                                                </li>
                                                <li><a class="dropdown-item" href="profile-booking.html">My Booking</a>
                                                </li>
                                                <li><a class="dropdown-item" href="profile-booking-history.html">My
                                                        Booking History</a></li>
                                                <li><a class="dropdown-item" href="profile-wishlist.html">My
                                                        Wishlist</a></li>
                                                <li><a class="dropdown-item" href="profile-wallet.html">My Wallet</a>
                                                </li>
                                                <li><a class="dropdown-item" href="profile-message.html">Messages</a>
                                                </li>
                                            </ul>
                                        </div>
                                        <div class="col-lg-3">
                                            <ul>
                                                <li><a class="dropdown-item"
                                                        href="profile-notification.html">Notifications</a></li>
                                                <li><a class="dropdown-item" href="profile-setting.html">Settings</a>
                                                </li>
                                                <li><a class="dropdown-item" href="about.html">About Us</a></li>
                                                <li><a class="dropdown-item" href="pricing.html">Pricing Plan</a></li>
                                                <li><a class="dropdown-item" href="team.html">Our Team</a></li>
                                                <li><a class="dropdown-item" href="service.html">Services</a></li>
                                                <li><a class="dropdown-item" href="service-single.html">Service
                                                        Single</a></li>
                                                <li><a class="dropdown-item" href="gallery.html">Gallery</a></li>
                                                <li><a class="dropdown-item" href="contact.html">Contact Us</a></li>
                                            </ul>
                                        </div>
                                        <div class="col-lg-3">
                                            <ul>
                                                <li><a class="dropdown-item" href="blog.html">Blog</a></li>
                                                <li><a class="dropdown-item" href="blog-single.html">Blog Single</a>
                                                </li>
                                                <li><a class="dropdown-item" href="faq.html">Faq's</a></li>
                                                <li><a class="dropdown-item" href="testimonial.html">Testimonials</a>
                                                </li>
                                                <li><a class="dropdown-item" href="404.html">404 Error</a></li>
                                                <li><a class="dropdown-item" href="coming-soon.html">Coming Soon</a>
                                                </li>
                                                <li><a class="dropdown-item" href="terms.html">Terms Of Service</a></li>
                                                <li><a class="dropdown-item" href="privacy.html">Privacy Policy</a></li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown">Activity</a>
                                <ul class="dropdown-menu fade-down">
                                    <li><a class="dropdown-item" href="activity-grid.html">Activity Grid</a></li>
                                    <li><a class="dropdown-item" href="activity-list.html">Activity List</a></li>
                                    <li><a class="dropdown-item" href="activity-full-width.html">Activity Full Width</a>
                                    </li>
                                    <li><a class="dropdown-item" href="activity-single.html">Activity Details</a></li>
                                    <li><a class="dropdown-item" href="activity-booking.html">Activity Booking</a></li>
                                    <li><a class="dropdown-item" href="activity-search-result.html">Activity Search
                                            Result</a></li>
                                    <li><a class="dropdown-item" href="activity-add.html">Add Activity</a></li>
                                </ul>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown">Car</a>
                                <ul class="dropdown-menu fade-down">
                                    <li><a class="dropdown-item" href="car-grid.html">Car Grid</a></li>
                                    <li><a class="dropdown-item" href="car-list.html">Car List</a></li>
                                    <li><a class="dropdown-item" href="car-full-width.html">Car Full Width</a></li>
                                    <li><a class="dropdown-item" href="car-single.html">Car Details</a></li>
                                    <li><a class="dropdown-item" href="car-booking.html">Car Booking</a></li>
                                    <li><a class="dropdown-item" href="car-search-result.html">Car Search Result</a>
                                    </li>
                                    <li><a class="dropdown-item" href="car-add.html">Add Car</a></li>
                                </ul>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown">Cruise</a>
                                <ul class="dropdown-menu fade-down">
                                    <li><a class="dropdown-item" href="cruise-grid.html">Cruise Grid</a></li>
                                    <li><a class="dropdown-item" href="cruise-list.html">Cruise List</a></li>
                                    <li><a class="dropdown-item" href="cruise-full-width.html">Cruise Full Width</a>
                                    </li>
                                    <li><a class="dropdown-item" href="cruise-single.html">Cruise Details</a></li>
                                    <li><a class="dropdown-item" href="cruise-booking.html">Cruise Booking</a></li>
                                    <li><a class="dropdown-item" href="cruise-search-result.html">Cruise Search
                                            Result</a></li>
                                    <li><a class="dropdown-item" href="cruise-add.html">Add Cruise</a></li>
                                </ul>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown">Tour</a>
                                <ul class="dropdown-menu fade-down">
                                    <li><a class="dropdown-item" href="tour-grid.html">Tour Grid</a></li>
                                    <li><a class="dropdown-item" href="tour-list.html">Tour List</a></li>
                                    <li><a class="dropdown-item" href="tour-full-width.html">Tour Full Width</a></li>
                                    <li><a class="dropdown-item" href="tour-single.html">Tour Details</a></li>
                                    <li><a class="dropdown-item" href="tour-booking.html">Tour Booking</a></li>
                                    <li><a class="dropdown-item" href="tour-search-result.html">Tour Search Result</a>
                                    </li>
                                    <li><a class="dropdown-item" href="tour-add.html">Add Tour</a></li>
                                </ul>
                            </li>
                            <li class="nav-item"><a class="nav-link" href="contact.html">Contact</a></li>
                        </ul>
                        <div class="header-nav-right">
                            <div class="header-btn">
                                <a href="become-expert.html" class="theme-btn mt-2">Become An Expert</a>
                            </div>
                        </div>
                    </div>
                </div>
            </nav>
        </div>
        <!-- navbar end -->

    </header>
    <!-- header area end -->



    <main class="main">

       <!-- breadcrumb -->
        <div class="site-breadcrumb" style="background: url(assets/img/breadcrumb/01.jpg)">
            <div class="container">
                <h2 class="breadcrumb-title">2,350 Results Found</h2>
                <ul class="breadcrumb-menu">
                    <li><a href="index-2.html">Home</a></li>
                    <li class="active">Flight Search</li>
                </ul>
            </div>
        </div>
        <!-- breadcrumb end -->


        <!-- search area -->
        <div class="search-area flight-search">
            <div class="container">
                <div class="search-wrapper">
                    <!-- flight search -->
                    <div class="search-box">
                        <div class="search-form">
                            <form action="#">
                                <!-- flight type -->
                                <div class="flight-type">
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" checked value="one-way"
                                            name="flight-type" id="flight-type1">
                                        <label class="form-check-label" for="flight-type1">
                                            One Way
                                        </label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" value="round-way"
                                            name="flight-type" id="flight-type2">
                                        <label class="form-check-label" for="flight-type2">
                                            Round Way
                                        </label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" value="multi-city"
                                            name="flight-type" id="flight-type3">
                                        <label class="form-check-label" for="flight-type3">
                                            Multi City
                                        </label>
                                    </div>
                                </div>
                                <!-- flight type end -->

                                <!-- flight search wrapper -->
                                <div class="flight-search-wrapper">
                                    <div class="flight-search-content">
                                        <!-- flight search content -->
                                        <div class="flight-search-item">
                                            <div class="row">
                                                <div class="col-lg-3">
                                                    <div class="form-group">
                                                        <label>From</label>
                                                        <div class="form-group-icon">
                                                            <input type="text" name="from-destination"
                                                                class="form-control swap-from" value="New York">
                                                            <i class="fal fa-plane-departure"></i>
                                                        </div>
                                                        <p>JFK - John F. Kennedy International Airport
                                                        </p>
                                                    </div>
                                                </div>
                                                <div class="col-lg-3">
                                                    <div class="form-group">
                                                        <div class="search-form-swap"><i class="far fa-repeat"></i>
                                                        </div>
                                                        <label>To</label>
                                                        <div class="form-group-icon">
                                                            <input type="text" name="to-destination"
                                                                class="form-control swap-to" value="Los Angeles">
                                                            <i class="fal fa-plane-arrival"></i>
                                                        </div>
                                                        <p>LAX - Los Angeles International Airport</p>
                                                    </div>
                                                </div>
                                                <div class="col-lg-3">
                                                    <div class="form-group">
                                                        <div class="search-form-date">
                                                            <div class="search-form-journey">
                                                                <label>Journey Date</label>
                                                                <div class="form-group-icon">
                                                                    <input type="text" name="journey-date"
                                                                        class="form-control date-picker journey-date">
                                                                    <i class="fal fa-calendar-days"></i>
                                                                </div>
                                                                <p class="journey-day-name"></p>
                                                            </div>
                                                            <div class="search-form-return">
                                                                <label>Return Date</label>
                                                                <div class="form-group-icon">
                                                                    <input type="text" name="return-date"
                                                                        class="form-control date-picker return-date">
                                                                </div>
                                                                <p class="return-day-name"></p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-lg-3">
                                                    <div class="form-group dropdown static-dropdown passenger-box">
                                                        <div class="passenger-class" role="menu" data-bs-toggle="dropdown"
                                                            data-bs-display="static" aria-expanded="false">
                                                            <label>Passenger, Class</label>
                                                            <div class="form-group-icon">
                                                                <div class="passenger-total"><span
                                                                        class="passenger-total-amount">2</span>
                                                                    Passenger
                                                                </div>
                                                                <i class="fal fa-user-tie-hair"></i>
                                                            </div>
                                                            <p class="passenger-class-name">Business</p>
                                                        </div>
                                                        <div class="dropdown-menu dropdown-menu-end">
                                                            <div class="dropdown-item">
                                                                <div class="passenger-item">
                                                                    <div class="passenger-info">
                                                                        <h6>Adults</h6>
                                                                        <p>12+ Years</p>
                                                                    </div>
                                                                    <div class="passenger-qty">
                                                                        <button type="button" class="minus-btn"><i
                                                                                class="far fa-minus"></i></button>
                                                                        <input type="text" name="adult"
                                                                            class="qty-amount passenger-adult" value="2"
                                                                            readonly>
                                                                        <button type="button" class="plus-btn"><i
                                                                                class="far fa-plus"></i></button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="dropdown-item">
                                                                <div class="passenger-item">
                                                                    <div class="passenger-info">
                                                                        <h6>Children</h6>
                                                                        <p>2-12 Years</p>
                                                                    </div>
                                                                    <div class="passenger-qty">
                                                                        <button type="button" class="minus-btn"><i
                                                                                class="far fa-minus"></i></button>
                                                                        <input type="text" name="children"
                                                                            class="qty-amount passenger-children"
                                                                            value="0" readonly>
                                                                        <button type="button" class="plus-btn"><i
                                                                                class="far fa-plus"></i></button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="dropdown-item">
                                                                <div class="passenger-item">
                                                                    <div class="passenger-info">
                                                                        <h6>Infant</h6>
                                                                        <p>Below 2 Years</p>
                                                                    </div>
                                                                    <div class="passenger-qty">
                                                                        <button type="button" class="minus-btn"><i
                                                                                class="far fa-minus"></i></button>
                                                                        <input type="text" name="infant"
                                                                            class="qty-amount passenger-infant"
                                                                            value="0" readonly>
                                                                        <button type="button" class="plus-btn"><i
                                                                                class="far fa-plus"></i></button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="dropdown-item">
                                                                <h6 class="mb-3 mt-2">Cabin Class</h6>
                                                                <div class="passenger-class-info">
                                                                    <div class="form-check">
                                                                        <input class="form-check-input" type="radio"
                                                                            value="Economy" name="cabin-class"
                                                                            id="cabin-class1">
                                                                        <label class="form-check-label"
                                                                            for="cabin-class1">
                                                                            Economy
                                                                        </label>
                                                                    </div>
                                                                    <div class="form-check">
                                                                        <input class="form-check-input" checked
                                                                            type="radio" value="Business"
                                                                            name="cabin-class" id="cabin-class2">
                                                                        <label class="form-check-label"
                                                                            for="cabin-class2">
                                                                            Business
                                                                        </label>
                                                                    </div>
                                                                    <div class="form-check">
                                                                        <input class="form-check-input" type="radio"
                                                                            value="First Class" name="cabin-class"
                                                                            id="cabin-class3">
                                                                        <label class="form-check-label"
                                                                            for="cabin-class3">
                                                                            First Class
                                                                        </label>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- flight search content end -->

                                        <!-- flight-multicity-item -->
                                        <div class="flight-search-item flight-multicity-item have-to-clone">
                                            <div class="row">
                                                <div class="col-lg-3">
                                                    <div class="form-group">
                                                        <label>From</label>
                                                        <div class="form-group-icon">
                                                            <input type="text" name="from-destination"
                                                                class="form-control swap-from" value="New York">
                                                            <i class="fal fa-plane-departure"></i>
                                                        </div>
                                                        <p>JFK - John F. Kennedy International Airport
                                                        </p>
                                                    </div>
                                                </div>
                                                <div class="col-lg-3">
                                                    <div class="form-group">
                                                        <div class="search-form-swap"><i class="far fa-repeat"></i>
                                                        </div>
                                                        <label>To</label>
                                                        <div class="form-group-icon">
                                                            <input type="text" name="to-destination"
                                                                class="form-control swap-to" value="Los Angeles">
                                                            <i class="fal fa-plane-arrival"></i>
                                                        </div>
                                                        <p>LAX - Los Angeles International Airport</p>
                                                    </div>
                                                </div>
                                                <div class="col-lg-3">
                                                    <div class="form-group">
                                                        <div class="search-form-date">
                                                            <div class="search-form-journey">
                                                                <label>Journey Date</label>
                                                                <div class="form-group-icon">
                                                                    <input type="text" name="journey-date"
                                                                        class="form-control date-picker journey-date">
                                                                    <i class="fal fa-calendar-days"></i>
                                                                </div>
                                                                <p class="journey-day-name"></p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-lg-3">
                                                    <div class="form-group">
                                                        <div class="multicity-btn">
                                                            <i class="fal fa-plus-circle"></i> Add
                                                            Another Flight
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- flight multicity end -->
                                    </div>
                                    <div class="search-btn">
                                        <button type="submit" class="theme-btn"><span
                                                class="far fa-search"></span>Update Search</button>
                                    </div>
                                </div>
                                <!-- flight search wrapper end -->
                            </form>
                        </div>
                    </div>
                    <!-- flight search end -->
                </div>
            </div>
        </div>
        <!-- search area end -->


        <!-- flight booking -->
        <div class="flight-booking flight-list pt-80 pb-120">
            <div class="container">
                <div class="row">
                    <!-- booking sidebar -->
                    <div class="col-lg-4 col-xl-3 mb-4">
                        <div class="booking-sidebar">
                            <div class="booking-item">
                                <h4 class="booking-title">Flight Class</h4>
                                <div class="flight-class">
                                    <div class="form-check">
                                        <input class="form-check-input" name="flight-class" type="checkbox" value="1"
                                            id="flight-class1">
                                        <label class="form-check-label" for="flight-class1">
                                            Business <span>(20)</span>
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" name="flight-class" type="checkbox" value="2"
                                            id="flight-class2">
                                        <label class="form-check-label" for="flight-class2">
                                            First Class <span>(15)</span>
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" name="flight-class" type="checkbox" value="3"
                                            id="flight-class3">
                                        <label class="form-check-label" for="flight-class3">
                                            Economy <span>(18)</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="booking-item">
                                <h4 class="booking-title">Flight Price</h4>
                                <div class="flight-price">
                                    <div class="price-range-slider">
                                        <div class="price-range-info">
                                            <label for="priceRange1">Price:</label>
                                            <input type="text" class="priceRange" id="priceRange1" readonly>
                                        </div>
                                        <div id="price-range1" class="price-range slider"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="booking-item">
                                <h4 class="booking-title">Flight Time</h4>
                                <div class="flight-time">
                                    <div class="form-check">
                                        <input class="form-check-input" name="flight-time" type="checkbox" value="1"
                                            id="flight-time1">
                                        <label class="form-check-label" for="flight-time1">
                                            <i class="far fa-sunrise"></i> 00:00 - 05:59 <span>(20)</span>
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" name="flight-time" type="checkbox" value="2"
                                            id="flight-time2">
                                        <label class="form-check-label" for="flight-time2">
                                            <i class="far fa-sun-bright"></i> 06:00 - 11:59 <span>(15)</span>
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" name="flight-time" type="checkbox" value="3"
                                            id="flight-time3">
                                        <label class="form-check-label" for="flight-time3">
                                            <i class="far fa-sunset"></i> 12:00 - 17:59 <span>(18)</span>
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" name="flight-time" type="checkbox" value="3"
                                            id="flight-time4">
                                        <label class="form-check-label" for="flight-time4">
                                            <i class="far fa-moon-stars"></i> 18:00 - 23:59 <span>(21)</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="booking-item">
                                <h4 class="booking-title">Flight Stops</h4>
                                <div class="flight-stop">
                                    <div class="form-check">
                                        <input class="form-check-input" name="flight-stop" type="checkbox" value="1"
                                            id="flight-stop1">
                                        <label class="form-check-label" for="flight-stop1">
                                            Non Stop <span>(20)</span>
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" name="flight-stop" type="checkbox" value="2"
                                            id="flight-stop2">
                                        <label class="form-check-label" for="flight-stop2">
                                            1 Stop <span>(15)</span>
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" name="flight-stop" type="checkbox" value="3"
                                            id="flight-stop3">
                                        <label class="form-check-label" for="flight-stop3">
                                            2 Stop <span>(18)</span>
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" name="flight-stop" type="checkbox" value="4"
                                            id="flight-stop4">
                                        <label class="form-check-label" for="flight-stop4">
                                            3 Stop <span>(25)</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="booking-item">
                                <h4 class="booking-title">Airlines</h4>
                                <div class="flight-airline">
                                    <div class="form-check">
                                        <input class="form-check-input" name="flight-airline" type="checkbox" value="1"
                                            id="flight-airline1">
                                        <label class="form-check-label" for="flight-airline1">
                                            American Airlines <span>(20)</span>
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" name="flight-airline" type="checkbox" value="2"
                                            id="flight-airline2">
                                        <label class="form-check-label" for="flight-airline2">
                                            Delta Airlines <span>(15)</span>
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" name="flight-airline" type="checkbox" value="3"
                                            id="flight-airline3">
                                        <label class="form-check-label" for="flight-airline3">
                                            Qatar Airways <span>(18)</span>
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" name="flight-airline" type="checkbox" value="4"
                                            id="flight-airline4">
                                        <label class="form-check-label" for="flight-airline4">
                                            Fly Amirates <span>(25)</span>
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" name="flight-airline" type="checkbox" value="5"
                                            id="flight-airline5">
                                        <label class="form-check-label" for="flight-airline5">
                                            Singapore Airlines <span>(35)</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="booking-item">
                                <h4 class="booking-title">Weights</h4>
                                <div class="flight-weight">
                                    <div class="form-check">
                                        <input class="form-check-input" name="flight-weight" type="checkbox" value="1"
                                            id="flight-weight1">
                                        <label class="form-check-label" for="flight-weight1">
                                            25 Kg <span>(20)</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="booking-item">
                                <h4 class="booking-title">Refundable</h4>
                                <div class="flight-refundable">
                                    <div class="form-check">
                                        <input class="form-check-input" name="flight-refundable" type="checkbox"
                                            value="1" id="flight-refundable1">
                                        <label class="form-check-label" for="flight-refundable1">
                                            Non Refundable <span>(20)</span>
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" name="flight-refundable" type="checkbox"
                                            value="2" id="flight-refundable2">
                                        <label class="form-check-label" for="flight-refundable2">
                                            Refundable <span>(15)</span>
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" name="flight-refundable" type="checkbox"
                                            value="3" id="flight-refundable3">
                                        <label class="form-check-label" for="flight-refundable3">
                                            As Per Rules <span>(18)</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- booking list -->
                    <div class="col-lg-8 col-xl-9">
                        <div class="col-md-12">
                            <div class="booking-sort">
                                <h5>2,350 Results Found</h5>
                                <div class="booking-sort-list-grid">
                                    <a class="booking-sort-grid" href="flight-grid.html"><i
                                            class="far fa-grid-2"></i></a>
                                    <a class="booking-sort-list active" href="flight-list.html"><i
                                            class="far fa-list-ul"></i></a>
                                </div>
                                <div class="col-md-3 booking-sort-box">
                                    <select class="select">
                                        <option value="1">Sort By Default</option>
                                        <option value="2">Sort By Popular</option>
                                        <option value="3">Sort By Low Price</option>
                                        <option value="4">Sort By High Price</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">

                            <!-- flight booking item -->
                            <div class="col-lg-12">
                                <div class="flight-booking-item">
                                    <div class="flight-booking-wrapper">
                                        <div class="flight-booking-info">
                                            <div class="flight-booking-content">
                                                <div class="flight-booking-airline">
                                                    <div class="flight-airline-img">
                                                        <img src="assets/img/flight/airline-7.png" alt="">
                                                    </div>
                                                    <h5 class="flight-airline-name">Delta</h5>
                                                </div>
                                                <div class="flight-booking-time">
                                                    <div class="start-time">
                                                        <div class="start-time-icon">
                                                            <i class="fal fa-plane-departure"></i>
                                                        </div>
                                                        <div class="start-time-info">
                                                            <h6 class="start-time-text">07:30</h6>
                                                            <span class="flight-destination">JFK</span>
                                                        </div>
                                                    </div>
                                                    <div class="flight-stop">
                                                        <span class="flight-stop-number">Non Stop</span>
                                                        <div class="flight-stop-arrow"></div>
                                                    </div>
                                                    <div class="end-time">
                                                        <div class="start-time-icon">
                                                            <i class="fal fa-plane-arrival"></i>
                                                        </div>
                                                        <div class="start-time-info">
                                                            <h6 class="end-time-text">08:35</h6>
                                                            <span class="flight-destination">LAX</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="flight-booking-duration">
                                                    <span class="duration-text">4h 05m</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flight-booking-price">
                                            <div class="price-info">
                                                <del class="discount-price">$5,548</del>
                                                <span class="price-amount">$4,548</span>
                                            </div>
                                            <a href="#" class="theme-btn">Book Now<i class="fas fa-arrow-circle-right"></i></a>
                                        </div>
                                    </div>
                                    <div class="flight-booking-detail">
                                        <div class="flight-booking-detail-header">
                                            <p>Partially Refundable</p>
                                            <a href="#flight-booking-collapse1" data-bs-toggle="collapse" role="button"
                                                aria-expanded="false" aria-controls="flight-booking-collapse1">Flight
                                                Details <i class="far fa-angle-down"></i></a>
                                        </div>
                                        <div class="collapse" id="flight-booking-collapse1">
                                            <div class="flight-booking-detail-wrapper">
                                                <div class="row">
                                                    <div class="col-lg-12 col-xl-6">
                                                        <div class="flight-booking-detail-left">
                                                            <ul class="nav nav-tabs" id="flTab1" role="tablist">
                                                                <li class="nav-item" role="presentation">
                                                                    <button class="nav-link active" id="fl-tab1"
                                                                        data-bs-toggle="tab"
                                                                        data-bs-target="#fl-tab-pane1" type="button"
                                                                        role="tab" aria-controls="fl-tab-pane1"
                                                                        aria-selected="true">JFK - LAX</button>
                                                                </li>
                                                            </ul>
                                                            <div class="tab-content" id="flTabContent1">
                                                                <div class="tab-pane fade show active"
                                                                    id="fl-tab-pane1" role="tabpanel"
                                                                    aria-labelledby="fl-tab1" tabindex="0">
                                                                    <div class="flight-booking-detail-info">
                                                                        <div class="flight-booking-airline">
                                                                            <div class="flight-airline-img">
                                                                                <img src="assets/img/flight/airline-7.png" alt="">
                                                                            </div>
                                                                            <div class="flight-airline-info flex-grow-1">
                                                                                <h5 class="flight-airline-name">Delta Airline</h5>
                                                                                <span class="flight-airline-model">SG 143 | AT7</span>
                                                                            </div>
                                                                            <p class="flight-airline-class">( Economy )</p>
                                                                        </div>
                                                                        <div class="flight-booking-time">
                                                                            <div class="start-time">
                                                                                <div class="start-time-icon">
                                                                                    <i class="fal fa-plane-departure"></i>
                                                                                </div>
                                                                                <div class="start-time-info">
                                                                                    <h6 class="start-time-text">07:30</h6>
                                                                                    <p class="flight-full-date">Sat, 22 Oct, 2025</p>
                                                                                    <span class="flight-destination">JFK</span>
                                                                                </div>
                                                                            </div>
                                                                            <div class="flight-stop">
                                                                                <span class="flight-stop-number">Non Stop</span>
                                                                                <div class="flight-stop-arrow"></div>
                                                                                <div class="flight-booking-duration">
                                                                                    <span class="duration-text">4h 05m</span>
                                                                                </div>
                                                                            </div>
                                                                            <div class="end-time">
                                                                                <div class="start-time-icon">
                                                                                    <i class="fal fa-plane-arrival"></i>
                                                                                </div>
                                                                                <div class="start-time-info">
                                                                                    <h6 class="end-time-text">08:35</h6>
                                                                                    <p class="flight-full-date">Sat, 25 Oct, 2025</p>
                                                                                    <span class="flight-destination">LAX</span>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-12 col-xl-6">
                                                        <div class="flight-booking-detail-right">
                                                            <ul class="nav nav-tabs" id="frTab1" role="tablist">
                                                                <li class="nav-item" role="presentation">
                                                                    <button class="nav-link active" id="fr-tab1"
                                                                        data-bs-toggle="tab"
                                                                        data-bs-target="#fr-tab-pane1" type="button"
                                                                        role="tab" aria-controls="fr-tab-pane1"
                                                                        aria-selected="true">Baggage</button>
                                                                </li>
                                                                <li class="nav-item" role="presentation">
                                                                    <button class="nav-link" id="fr-tab2"
                                                                        data-bs-toggle="tab"
                                                                        data-bs-target="#fr-tab-pane2" type="button"
                                                                        role="tab" aria-controls="fr-tab-pane2"
                                                                        aria-selected="false">Fare</button>
                                                                </li>
                                                                <li class="nav-item" role="presentation">
                                                                    <button class="nav-link" id="fr-tab3"
                                                                        data-bs-toggle="tab"
                                                                        data-bs-target="#fr-tab-pane3" type="button"
                                                                        role="tab" aria-controls="fr-tab-pane3"
                                                                        aria-selected="false">Policy</button>
                                                                </li>
                                                            </ul>
                                                            <div class="tab-content" id="frTabContent1">
                                                                <div class="tab-pane fade show active"
                                                                    id="fr-tab-pane1" role="tabpanel"
                                                                    aria-labelledby="fr-tab1" tabindex="0">
                                                                    <div class="flight-booking-detail-info">
                                                                        <table class="table table-borderless">
                                                                            <tr>
                                                                                <th>Flight</th>
                                                                                <th>Cabin</th>
                                                                                <th>Check In</th>
                                                                            </tr>
                                                                            <tr>
                                                                                <td>JFK - LAX</td>
                                                                                <td>7 Kilograms</td>
                                                                                <td>20 Kilograms</td>
                                                                            </tr>
                                                                        </table>
                                                                    </div>
                                                                </div>
                                                                <div class="tab-pane fade" id="fr-tab-pane2"
                                                                    role="tabpanel" aria-labelledby="fr-tab2"
                                                                    tabindex="0">
                                                                    <div class="flight-booking-detail-info">
                                                                        <table class="table table-borderless">
                                                                            <tr>
                                                                                <th>Fare Summary</th>
                                                                                <th>Base Fare</th>
                                                                                <th>Tax</th>
                                                                            </tr>
                                                                            <tr>
                                                                                <td>Adult x 1</td>
                                                                                <td>$5,423</td>
                                                                                <td>$1,000</td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td>Child x 1</td>
                                                                                <td>$3,423</td>
                                                                                <td>$1,000</td>
                                                                            </tr>
                                                                        </table>
                                                                    </div>
                                                                </div>
                                                                <div class="tab-pane fade" id="fr-tab-pane3"
                                                                    role="tabpanel" aria-labelledby="fr-tab3"
                                                                    tabindex="0">
                                                                    <div class="flight-booking-detail-info">
                                                                        <div class="flight-booking-policy">
                                                                            <ul>
                                                                                <li>
                                                                                   1. Refund and Date Change are done as per the following policies.
                                                                                </li>
                                                                                <li>
                                                                                   2. Refund Amount= Refund Charge (as per airline policy + ShareTrip Convenience Fee).
                                                                                </li>
                                                                                <li>
                                                                                   3. Date Change Amount= Date Change Fee (as per Airline Policy + ShareTrip Convenience Fee).
                                                                                </li>
                                                                            </ul>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="flight-booking-detail-price">
                                                                <h6 class="flight-booking-detail-price-title">Total (2 Traveler)</h6>
                                                                <div class="flight-detail-price-amount">
                                                                    $10,846
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- flight booking item -->
                            <div class="col-lg-12">
                                <div class="flight-booking-item">
                                    <div class="flight-booking-wrapper">
                                        <div class="flight-booking-info">
                                            <div class="flight-booking-content">
                                                <div class="flight-booking-airline">
                                                    <div class="flight-airline-img">
                                                        <img src="assets/img/flight/airline-7.png" alt="">
                                                    </div>
                                                    <h5 class="flight-airline-name">Delta</h5>
                                                </div>
                                                <div class="flight-booking-time">
                                                    <div class="start-time">
                                                        <div class="start-time-icon">
                                                            <i class="fal fa-plane-departure"></i>
                                                        </div>
                                                        <div class="start-time-info">
                                                            <h6 class="start-time-text">07:30</h6>
                                                            <span class="flight-destination">JFK</span>
                                                        </div>
                                                    </div>
                                                    <div class="flight-stop">
                                                        <span class="flight-stop-number">1 Stop (STN)</span>
                                                        <div class="flight-stop-arrow flight-has-stop"></div>
                                                    </div>
                                                    <div class="end-time">
                                                        <div class="start-time-icon">
                                                            <i class="fal fa-plane-arrival"></i>
                                                        </div>
                                                        <div class="start-time-info">
                                                            <h6 class="end-time-text">08:35</h6>
                                                            <span class="flight-destination">LAX</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="flight-booking-duration">
                                                    <span class="duration-text">4h 05m</span>
                                                </div>
                                            </div>
                                            <div class="flight-booking-content flight-booking-return">
                                                <div class="flight-booking-airline">
                                                    <div class="flight-airline-img">
                                                        <img src="assets/img/flight/airline-7.png" alt="">
                                                    </div>
                                                    <h5 class="flight-airline-name">Delta</h5>
                                                </div>
                                                <div class="flight-booking-time">
                                                    <div class="start-time">
                                                        <div class="start-time-icon">
                                                            <i class="fal fa-plane-departure"></i>
                                                        </div>
                                                        <div class="start-time-info">
                                                            <h6 class="start-time-text">07:30</h6>
                                                            <span class="flight-destination">JFK</span>
                                                        </div>
                                                    </div>
                                                    <div class="flight-stop">
                                                        <span class="flight-stop-number">1 Stop (STN)</span>
                                                        <div class="flight-stop-arrow flight-has-stop"></div>
                                                    </div>
                                                    <div class="end-time">
                                                        <div class="start-time-icon">
                                                            <i class="fal fa-plane-arrival"></i>
                                                        </div>
                                                        <div class="start-time-info">
                                                            <h6 class="end-time-text">08:35</h6>
                                                            <span class="flight-destination">LAX</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="flight-booking-duration">
                                                    <span class="duration-text">4h 05m</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flight-booking-price">
                                            <div class="price-info">
                                                <del class="discount-price">$5,548</del>
                                                <span class="price-amount">$4,548</span>
                                            </div>
                                            <a href="#" class="theme-btn">Book Now<i class="fas fa-arrow-circle-right"></i></a>
                                        </div>
                                    </div>
                                    <div class="flight-booking-detail">
                                        <div class="flight-booking-detail-header">
                                            <p>Partially Refundable</p>
                                            <a href="#flight-booking-collapse2" data-bs-toggle="collapse" role="button"
                                                aria-expanded="false" aria-controls="flight-booking-collapse2">Flight
                                                Details <i class="far fa-angle-down"></i></a>
                                        </div>
                                        <div class="collapse" id="flight-booking-collapse2">
                                            <div class="flight-booking-detail-wrapper">
                                                <div class="row">
                                                    <div class="col-lg-12 col-xl-6">
                                                        <div class="flight-booking-detail-left">
                                                            <ul class="nav nav-tabs" id="flTab2" role="tablist">
                                                                <li class="nav-item" role="presentation">
                                                                    <button class="nav-link active" id="fl-tab3"
                                                                        data-bs-toggle="tab"
                                                                        data-bs-target="#fl-tab-pane3" type="button"
                                                                        role="tab" aria-controls="fl-tab-pane3"
                                                                        aria-selected="true">JFK - LAX</button>
                                                                </li>
                                                                <li class="nav-item" role="presentation">
                                                                    <button class="nav-link" id="fl-tab4"
                                                                        data-bs-toggle="tab"
                                                                        data-bs-target="#fl-tab-pane4" type="button"
                                                                        role="tab" aria-controls="fl-tab-pane4"
                                                                        aria-selected="false">LAX - JFK</button>
                                                                </li>
                                                            </ul>
                                                            <div class="tab-content" id="flTabContent2">
                                                                <div class="tab-pane fade show active"
                                                                    id="fl-tab-pane3" role="tabpanel"
                                                                    aria-labelledby="fl-tab3" tabindex="0">
                                                                    <div class="flight-booking-detail-info">
                                                                        <div class="flight-booking-airline">
                                                                            <div class="flight-airline-img">
                                                                                <img src="assets/img/flight/airline-7.png" alt="">
                                                                            </div>
                                                                            <div class="flight-airline-info flex-grow-1">
                                                                                <h5 class="flight-airline-name">Delta Airline</h5>
                                                                                <span class="flight-airline-model">SG 143 | AT7</span>
                                                                            </div>
                                                                            <p class="flight-airline-class">( Economy )</p>
                                                                        </div>
                                                                        <div class="flight-booking-time">
                                                                            <div class="start-time">
                                                                                <div class="start-time-icon">
                                                                                    <i class="fal fa-plane-departure"></i>
                                                                                </div>
                                                                                <div class="start-time-info">
                                                                                    <h6 class="start-time-text">07:30</h6>
                                                                                    <p class="flight-full-date">Sat, 22 Oct, 2025</p>
                                                                                    <span class="flight-destination">JFK</span>
                                                                                </div>
                                                                            </div>
                                                                            <div class="flight-stop">
                                                                                <span class="flight-stop-number">1 Stop (STN)</span>
                                                                                <div class="flight-stop-arrow flight-has-stop"></div>
                                                                                <div class="flight-booking-duration">
                                                                                    <span class="duration-text">4h 05m</span>
                                                                                </div>
                                                                            </div>
                                                                            <div class="end-time">
                                                                                <div class="start-time-icon">
                                                                                    <i class="fal fa-plane-arrival"></i>
                                                                                </div>
                                                                                <div class="start-time-info">
                                                                                    <h6 class="end-time-text">08:35</h6>
                                                                                    <p class="flight-full-date">Sat, 25 Oct, 2025</p>
                                                                                    <span class="flight-destination">LAX</span>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div class="tab-pane fade" id="fl-tab-pane4"
                                                                    role="tabpanel" aria-labelledby="fl-tab4"
                                                                    tabindex="0">
                                                                    <div class="flight-booking-detail-info">
                                                                        <div class="flight-booking-airline">
                                                                            <div class="flight-airline-img">
                                                                                <img src="assets/img/flight/airline-7.png" alt="">
                                                                            </div>
                                                                            <div class="flight-airline-info flex-grow-1">
                                                                                <h5 class="flight-airline-name">Delta Airline</h5>
                                                                                <span class="flight-airline-model">SG 143 | AT7</span>
                                                                            </div>
                                                                            <p class="flight-airline-class">( Economy )</p>
                                                                        </div>
                                                                        <div class="flight-booking-time mt-0 flight-booking-return">
                                                                            <div class="start-time">
                                                                                <div class="start-time-icon">
                                                                                    <i class="fal fa-plane-departure"></i>
                                                                                </div>
                                                                                <div class="start-time-info">
                                                                                    <h6 class="start-time-text">07:30</h6>
                                                                                    <p class="flight-full-date">Sat, 22 Oct, 2025</p>
                                                                                    <span class="flight-destination">JFK</span>
                                                                                </div>
                                                                            </div>
                                                                            <div class="flight-stop">
                                                                                <span class="flight-stop-number">1 Stop (STN)</span>
                                                                                <div class="flight-stop-arrow flight-has-stop"></div>
                                                                                <div class="flight-booking-duration">
                                                                                    <span class="duration-text">4h 05m</span>
                                                                                </div>
                                                                            </div>
                                                                            <div class="end-time">
                                                                                <div class="start-time-icon">
                                                                                    <i class="fal fa-plane-arrival"></i>
                                                                                </div>
                                                                                <div class="start-time-info">
                                                                                    <h6 class="end-time-text">08:35</h6>
                                                                                    <p class="flight-full-date">Sat, 25 Oct, 2025</p>
                                                                                    <span class="flight-destination">LAX</span>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-12 col-xl-6">
                                                        <div class="flight-booking-detail-right">
                                                            <ul class="nav nav-tabs" id="frTab2" role="tablist">
                                                                <li class="nav-item" role="presentation">
                                                                    <button class="nav-link active" id="fr-tab4"
                                                                        data-bs-toggle="tab"
                                                                        data-bs-target="#fr-tab-pane4" type="button"
                                                                        role="tab" aria-controls="fr-tab-pane4"
                                                                        aria-selected="true">Baggage</button>
                                                                </li>
                                                                <li class="nav-item" role="presentation">
                                                                    <button class="nav-link" id="fr-tab5"
                                                                        data-bs-toggle="tab"
                                                                        data-bs-target="#fr-tab-pane5" type="button"
                                                                        role="tab" aria-controls="fr-tab-pane5"
                                                                        aria-selected="false">Fare</button>
                                                                </li>
                                                                <li class="nav-item" role="presentation">
                                                                    <button class="nav-link" id="fr-tab6"
                                                                        data-bs-toggle="tab"
                                                                        data-bs-target="#fr-tab-pane6" type="button"
                                                                        role="tab" aria-controls="fr-tab-pane6"
                                                                        aria-selected="false">Policy</button>
                                                                </li>
                                                            </ul>
                                                            <div class="tab-content" id="frTabContent4">
                                                                <div class="tab-pane fade show active"
                                                                    id="fr-tab-pane4" role="tabpanel"
                                                                    aria-labelledby="fr-tab1" tabindex="0">
                                                                    <div class="flight-booking-detail-info">
                                                                        <table class="table table-borderless">
                                                                            <tr>
                                                                                <th>Flight</th>
                                                                                <th>Cabin</th>
                                                                                <th>Check In</th>
                                                                            </tr>
                                                                            <tr>
                                                                                <td>JFK - LAX</td>
                                                                                <td>7 Kilograms</td>
                                                                                <td>20 Kilograms</td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td>LAX - JFK</td>
                                                                                <td>7 Kilograms</td>
                                                                                <td>20 Kilograms</td>
                                                                            </tr>
                                                                        </table>
                                                                    </div>
                                                                </div>
                                                                <div class="tab-pane fade" id="fr-tab-pane5"
                                                                    role="tabpanel" aria-labelledby="fr-tab5"
                                                                    tabindex="0">
                                                                    <div class="flight-booking-detail-info">
                                                                        <table class="table table-borderless">
                                                                            <tr>
                                                                                <th>Fare Summary</th>
                                                                                <th>Base Fare</th>
                                                                                <th>Tax</th>
                                                                            </tr>
                                                                            <tr>
                                                                                <td>Adult x 1</td>
                                                                                <td>$5,423</td>
                                                                                <td>$1,000</td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td>Child x 1</td>
                                                                                <td>$3,423</td>
                                                                                <td>$1,000</td>
                                                                            </tr>
                                                                        </table>
                                                                    </div>
                                                                </div>
                                                                <div class="tab-pane fade" id="fr-tab-pane6"
                                                                    role="tabpanel" aria-labelledby="fr-tab6"
                                                                    tabindex="0">
                                                                    <div class="flight-booking-detail-info">
                                                                        <div class="flight-booking-policy">
                                                                            <ul>
                                                                                <li>
                                                                                   1. Refund and Date Change are done as per the following policies.
                                                                                </li>
                                                                                <li>
                                                                                   2. Refund Amount= Refund Charge (as per airline policy + ShareTrip Convenience Fee).
                                                                                </li>
                                                                                <li>
                                                                                   3. Date Change Amount= Date Change Fee (as per Airline Policy + ShareTrip Convenience Fee).
                                                                                </li>
                                                                            </ul>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="flight-booking-detail-price">
                                                                <h6 class="flight-booking-detail-price-title">Total (2 Traveler)</h6>
                                                                <div class="flight-detail-price-amount">
                                                                    $10,846
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- flight booking item -->
                            <div class="col-lg-12">
                                <div class="flight-booking-item">
                                    <div class="flight-booking-wrapper">
                                        <div class="flight-booking-info">
                                            <div class="flight-booking-content">
                                                <div class="flight-booking-airline">
                                                    <div class="flight-airline-img">
                                                        <img src="assets/img/flight/airline-7.png" alt="">
                                                    </div>
                                                    <h5 class="flight-airline-name">Delta</h5>
                                                </div>
                                                <div class="flight-booking-time">
                                                    <div class="start-time">
                                                        <div class="start-time-icon">
                                                            <i class="fal fa-plane-departure"></i>
                                                        </div>
                                                        <div class="start-time-info">
                                                            <h6 class="start-time-text">07:30</h6>
                                                            <span class="flight-destination">JFK</span>
                                                        </div>
                                                    </div>
                                                    <div class="flight-stop">
                                                        <span class="flight-stop-number">Non Stop</span>
                                                        <div class="flight-stop-arrow"></div>
                                                    </div>
                                                    <div class="end-time">
                                                        <div class="start-time-icon">
                                                            <i class="fal fa-plane-arrival"></i>
                                                        </div>
                                                        <div class="start-time-info">
                                                            <h6 class="end-time-text">08:35</h6>
                                                            <span class="flight-destination">LAX</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="flight-booking-duration">
                                                    <span class="duration-text">4h 05m</span>
                                                </div>
                                            </div>
                                            <div class="flight-booking-content flight-booking-return">
                                                <div class="flight-booking-airline">
                                                    <div class="flight-airline-img">
                                                        <img src="assets/img/flight/airline-7.png" alt="">
                                                    </div>
                                                    <h5 class="flight-airline-name">Delta</h5>
                                                </div>
                                                <div class="flight-booking-time">
                                                    <div class="start-time">
                                                        <div class="start-time-icon">
                                                            <i class="fal fa-plane-departure"></i>
                                                        </div>
                                                        <div class="start-time-info">
                                                            <h6 class="start-time-text">07:30</h6>
                                                            <span class="flight-destination">JFK</span>
                                                        </div>
                                                    </div>
                                                    <div class="flight-stop">
                                                        <span class="flight-stop-number">Non Stop</span>
                                                        <div class="flight-stop-arrow"></div>
                                                    </div>
                                                    <div class="end-time">
                                                        <div class="start-time-icon">
                                                            <i class="fal fa-plane-arrival"></i>
                                                        </div>
                                                        <div class="start-time-info">
                                                            <h6 class="end-time-text">08:35</h6>
                                                            <span class="flight-destination">LAX</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="flight-booking-duration">
                                                    <span class="duration-text">4h 05m</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flight-booking-price">
                                            <div class="price-info">
                                                <del class="discount-price">$5,548</del>
                                                <span class="price-amount">$4,548</span>
                                            </div>
                                            <a href="#" class="theme-btn">Book Now<i class="fas fa-arrow-circle-right"></i></a>
                                        </div>
                                    </div>
                                    <div class="flight-booking-detail">
                                        <div class="flight-booking-detail-header">
                                            <p>Partially Refundable</p>
                                            <a href="#flight-booking-collapse3" data-bs-toggle="collapse" role="button"
                                                aria-expanded="false" aria-controls="flight-booking-collapse3">Flight
                                                Details <i class="far fa-angle-down"></i></a>
                                        </div>
                                        <div class="collapse" id="flight-booking-collapse3">
                                            <div class="flight-booking-detail-wrapper">
                                                <div class="row">
                                                    <div class="col-lg-12 col-xl-6">
                                                        <div class="flight-booking-detail-left">
                                                            <ul class="nav nav-tabs" id="flTab3" role="tablist">
                                                                <li class="nav-item" role="presentation">
                                                                    <button class="nav-link active" id="fl-tab7"
                                                                        data-bs-toggle="tab"
                                                                        data-bs-target="#fl-tab-pane7" type="button"
                                                                        role="tab" aria-controls="fl-tab-pane7"
                                                                        aria-selected="true">JFK - LAX</button>
                                                                </li>
                                                                <li class="nav-item" role="presentation">
                                                                    <button class="nav-link" id="fl-tab8"
                                                                        data-bs-toggle="tab"
                                                                        data-bs-target="#fl-tab-pane8" type="button"
                                                                        role="tab" aria-controls="fl-tab-pane8"
                                                                        aria-selected="false">LAX - JFK</button>
                                                                </li>
                                                            </ul>
                                                            <div class="tab-content" id="flTabContent3">
                                                                <div class="tab-pane fade show active"
                                                                    id="fl-tab-pane7" role="tabpanel"
                                                                    aria-labelledby="fl-tab7" tabindex="0">
                                                                    <div class="flight-booking-detail-info">
                                                                        <div class="flight-booking-airline">
                                                                            <div class="flight-airline-img">
                                                                                <img src="assets/img/flight/airline-7.png" alt="">
                                                                            </div>
                                                                            <div class="flight-airline-info flex-grow-1">
                                                                                <h5 class="flight-airline-name">Delta Airline</h5>
                                                                                <span class="flight-airline-model">SG 143 | AT7</span>
                                                                            </div>
                                                                            <p class="flight-airline-class">( Economy )</p>
                                                                        </div>
                                                                        <div class="flight-booking-time">
                                                                            <div class="start-time">
                                                                                <div class="start-time-icon">
                                                                                    <i class="fal fa-plane-departure"></i>
                                                                                </div>
                                                                                <div class="start-time-info">
                                                                                    <h6 class="start-time-text">07:30</h6>
                                                                                    <p class="flight-full-date">Sat, 22 Oct, 2025</p>
                                                                                    <span class="flight-destination">JFK</span>
                                                                                </div>
                                                                            </div>
                                                                            <div class="flight-stop">
                                                                                <span class="flight-stop-number">Non Stop</span>
                                                                                <div class="flight-stop-arrow"></div>
                                                                                <div class="flight-booking-duration">
                                                                                    <span class="duration-text">4h 05m</span>
                                                                                </div>
                                                                            </div>
                                                                            <div class="end-time">
                                                                                <div class="start-time-icon">
                                                                                    <i class="fal fa-plane-arrival"></i>
                                                                                </div>
                                                                                <div class="start-time-info">
                                                                                    <h6 class="end-time-text">08:35</h6>
                                                                                    <p class="flight-full-date">Sat, 25 Oct, 2025</p>
                                                                                    <span class="flight-destination">LAX</span>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div class="tab-pane fade" id="fl-tab-pane8"
                                                                    role="tabpanel" aria-labelledby="fl-tab8"
                                                                    tabindex="0">
                                                                    <div class="flight-booking-detail-info">
                                                                        <div class="flight-booking-airline">
                                                                            <div class="flight-airline-img">
                                                                                <img src="assets/img/flight/airline-7.png" alt="">
                                                                            </div>
                                                                            <div class="flight-airline-info flex-grow-1">
                                                                                <h5 class="flight-airline-name">Delta Airline</h5>
                                                                                <span class="flight-airline-model">SG 143 | AT7</span>
                                                                            </div>
                                                                            <p class="flight-airline-class">( Economy )</p>
                                                                        </div>
                                                                        <div class="flight-booking-time mt-0 flight-booking-return">
                                                                            <div class="start-time">
                                                                                <div class="start-time-icon">
                                                                                    <i class="fal fa-plane-departure"></i>
                                                                                </div>
                                                                                <div class="start-time-info">
                                                                                    <h6 class="start-time-text">07:30</h6>
                                                                                    <p class="flight-full-date">Sat, 22 Oct, 2025</p>
                                                                                    <span class="flight-destination">JFK</span>
                                                                                </div>
                                                                            </div>
                                                                            <div class="flight-stop">
                                                                                <span class="flight-stop-number">Non Stop</span>
                                                                                <div class="flight-stop-arrow"></div>
                                                                                <div class="flight-booking-duration">
                                                                                    <span class="duration-text">4h 05m</span>
                                                                                </div>
                                                                            </div>
                                                                            <div class="end-time">
                                                                                <div class="start-time-icon">
                                                                                    <i class="fal fa-plane-arrival"></i>
                                                                                </div>
                                                                                <div class="start-time-info">
                                                                                    <h6 class="end-time-text">08:35</h6>
                                                                                    <p class="flight-full-date">Sat, 25 Oct, 2025</p>
                                                                                    <span class="flight-destination">LAX</span>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-12 col-xl-6">
                                                        <div class="flight-booking-detail-right">
                                                            <ul class="nav nav-tabs" id="frTab3" role="tablist">
                                                                <li class="nav-item" role="presentation">
                                                                    <button class="nav-link active" id="fr-tab7"
                                                                        data-bs-toggle="tab"
                                                                        data-bs-target="#fr-tab-pane7" type="button"
                                                                        role="tab" aria-controls="fr-tab-pane7"
                                                                        aria-selected="true">Baggage</button>
                                                                </li>
                                                                <li class="nav-item" role="presentation">
                                                                    <button class="nav-link" id="fr-tab8"
                                                                        data-bs-toggle="tab"
                                                                        data-bs-target="#fr-tab-pane8" type="button"
                                                                        role="tab" aria-controls="fr-tab-pane8"
                                                                        aria-selected="false">Fare</button>
                                                                </li>
                                                                <li class="nav-item" role="presentation">
                                                                    <button class="nav-link" id="fr-tab9"
                                                                        data-bs-toggle="tab"
                                                                        data-bs-target="#fr-tab-pane9" type="button"
                                                                        role="tab" aria-controls="fr-tab-pane9"
                                                                        aria-selected="false">Policy</button>
                                                                </li>
                                                            </ul>
                                                            <div class="tab-content" id="frTabContent3">
                                                                <div class="tab-pane fade show active"
                                                                    id="fr-tab-pane7" role="tabpanel"
                                                                    aria-labelledby="fr-tab7" tabindex="0">
                                                                    <div class="flight-booking-detail-info">
                                                                        <table class="table table-borderless">
                                                                            <tr>
                                                                                <th>Flight</th>
                                                                                <th>Cabin</th>
                                                                                <th>Check In</th>
                                                                            </tr>
                                                                            <tr>
                                                                                <td>JFK - LAX</td>
                                                                                <td>7 Kilograms</td>
                                                                                <td>20 Kilograms</td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td>LAX - JFK</td>
                                                                                <td>7 Kilograms</td>
                                                                                <td>20 Kilograms</td>
                                                                            </tr>
                                                                        </table>
                                                                    </div>
                                                                </div>
                                                                <div class="tab-pane fade" id="fr-tab-pane8"
                                                                    role="tabpanel" aria-labelledby="fr-tab8"
                                                                    tabindex="0">
                                                                    <div class="flight-booking-detail-info">
                                                                        <table class="table table-borderless">
                                                                            <tr>
                                                                                <th>Fare Summary</th>
                                                                                <th>Base Fare</th>
                                                                                <th>Tax</th>
                                                                            </tr>
                                                                            <tr>
                                                                                <td>Adult x 1</td>
                                                                                <td>$5,423</td>
                                                                                <td>$1,000</td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td>Child x 1</td>
                                                                                <td>$3,423</td>
                                                                                <td>$1,000</td>
                                                                            </tr>
                                                                        </table>
                                                                    </div>
                                                                </div>
                                                                <div class="tab-pane fade" id="fr-tab-pane9"
                                                                    role="tabpanel" aria-labelledby="fr-tab9"
                                                                    tabindex="0">
                                                                    <div class="flight-booking-detail-info">
                                                                        <div class="flight-booking-policy">
                                                                            <ul>
                                                                                <li>
                                                                                   1. Refund and Date Change are done as per the following policies.
                                                                                </li>
                                                                                <li>
                                                                                   2. Refund Amount= Refund Charge (as per airline policy + ShareTrip Convenience Fee).
                                                                                </li>
                                                                                <li>
                                                                                   3. Date Change Amount= Date Change Fee (as per Airline Policy + ShareTrip Convenience Fee).
                                                                                </li>
                                                                            </ul>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="flight-booking-detail-price">
                                                                <h6 class="flight-booking-detail-price-title">Total (2 Traveler)</h6>
                                                                <div class="flight-detail-price-amount">
                                                                    $10,846
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- flight booking item -->
                            <div class="col-lg-12">
                                <div class="flight-booking-item">
                                    <div class="flight-booking-wrapper">
                                        <div class="flight-booking-info">
                                            <div class="flight-booking-content">
                                                <div class="flight-booking-airline">
                                                    <div class="flight-airline-img">
                                                        <img src="assets/img/flight/airline-7.png" alt="">
                                                    </div>
                                                    <h5 class="flight-airline-name">Delta</h5>
                                                </div>
                                                <div class="flight-booking-time">
                                                    <div class="start-time">
                                                        <div class="start-time-icon">
                                                            <i class="fal fa-plane-departure"></i>
                                                        </div>
                                                        <div class="start-time-info">
                                                            <h6 class="start-time-text">07:30</h6>
                                                            <span class="flight-destination">JFK</span>
                                                        </div>
                                                    </div>
                                                    <div class="flight-stop">
                                                        <span class="flight-stop-number">Non Stop</span>
                                                        <div class="flight-stop-arrow"></div>
                                                    </div>
                                                    <div class="end-time">
                                                        <div class="start-time-icon">
                                                            <i class="fal fa-plane-arrival"></i>
                                                        </div>
                                                        <div class="start-time-info">
                                                            <h6 class="end-time-text">08:35</h6>
                                                            <span class="flight-destination">LAX</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="flight-booking-duration">
                                                    <span class="duration-text">4h 05m</span>
                                                </div>
                                            </div>
                                            <div class="flight-booking-content flight-booking-return">
                                                <div class="flight-booking-airline">
                                                    <div class="flight-airline-img">
                                                        <img src="assets/img/flight/airline-7.png" alt="">
                                                    </div>
                                                    <h5 class="flight-airline-name">Delta</h5>
                                                </div>
                                                <div class="flight-booking-time">
                                                    <div class="start-time">
                                                        <div class="start-time-icon">
                                                            <i class="fal fa-plane-departure"></i>
                                                        </div>
                                                        <div class="start-time-info">
                                                            <h6 class="start-time-text">07:30</h6>
                                                            <span class="flight-destination">JFK</span>
                                                        </div>
                                                    </div>
                                                    <div class="flight-stop">
                                                        <span class="flight-stop-number">Non Stop</span>
                                                        <div class="flight-stop-arrow"></div>
                                                    </div>
                                                    <div class="end-time">
                                                        <div class="start-time-icon">
                                                            <i class="fal fa-plane-arrival"></i>
                                                        </div>
                                                        <div class="start-time-info">
                                                            <h6 class="end-time-text">08:35</h6>
                                                            <span class="flight-destination">LAX</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="flight-booking-duration">
                                                    <span class="duration-text">4h 05m</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flight-booking-price">
                                            <div class="price-info">
                                                <span class="price-amount">$4,548</span>
                                            </div>
                                            <a href="#" class="theme-btn">Book Now<i class="fas fa-arrow-circle-right"></i></a>
                                        </div>
                                    </div>
                                    <div class="flight-booking-detail">
                                        <div class="flight-booking-detail-header">
                                            <p>Partially Refundable</p>
                                            <a href="#flight-booking-collapse4" data-bs-toggle="collapse" role="button"
                                                aria-expanded="false" aria-controls="flight-booking-collapse4">Flight
                                                Details <i class="far fa-angle-down"></i></a>
                                        </div>
                                        <div class="collapse" id="flight-booking-collapse4">
                                            <div class="flight-booking-detail-wrapper">
                                                <div class="row">
                                                    <div class="col-lg-12 col-xl-6">
                                                        <div class="flight-booking-detail-left">
                                                            <ul class="nav nav-tabs" id="flTab4" role="tablist">
                                                                <li class="nav-item" role="presentation">
                                                                    <button class="nav-link active" id="fl-tab9"
                                                                        data-bs-toggle="tab"
                                                                        data-bs-target="#fl-tab-pane9" type="button"
                                                                        role="tab" aria-controls="fl-tab-pane9"
                                                                        aria-selected="true">JFK - LAX</button>
                                                                </li>
                                                                <li class="nav-item" role="presentation">
                                                                    <button class="nav-link" id="fl-tab10"
                                                                        data-bs-toggle="tab"
                                                                        data-bs-target="#fl-tab-pane10" type="button"
                                                                        role="tab" aria-controls="fl-tab-pane10"
                                                                        aria-selected="false">LAX - JFK</button>
                                                                </li>
                                                            </ul>
                                                            <div class="tab-content" id="flTabContent4">
                                                                <div class="tab-pane fade show active"
                                                                    id="fl-tab-pane9" role="tabpanel"
                                                                    aria-labelledby="fl-tab9" tabindex="0">
                                                                    <div class="flight-booking-detail-info">
                                                                        <div class="flight-booking-airline">
                                                                            <div class="flight-airline-img">
                                                                                <img src="assets/img/flight/airline-7.png" alt="">
                                                                            </div>
                                                                            <div class="flight-airline-info flex-grow-1">
                                                                                <h5 class="flight-airline-name">Delta Airline</h5>
                                                                                <span class="flight-airline-model">SG 143 | AT7</span>
                                                                            </div>
                                                                            <p class="flight-airline-class">( Economy )</p>
                                                                        </div>
                                                                        <div class="flight-booking-time">
                                                                            <div class="start-time">
                                                                                <div class="start-time-icon">
                                                                                    <i class="fal fa-plane-departure"></i>
                                                                                </div>
                                                                                <div class="start-time-info">
                                                                                    <h6 class="start-time-text">07:30</h6>
                                                                                    <p class="flight-full-date">Sat, 22 Oct, 2025</p>
                                                                                    <span class="flight-destination">JFK</span>
                                                                                </div>
                                                                            </div>
                                                                            <div class="flight-stop">
                                                                                <span class="flight-stop-number">Non Stop</span>
                                                                                <div class="flight-stop-arrow"></div>
                                                                                <div class="flight-booking-duration">
                                                                                    <span class="duration-text">4h 05m</span>
                                                                                </div>
                                                                            </div>
                                                                            <div class="end-time">
                                                                                <div class="start-time-icon">
                                                                                    <i class="fal fa-plane-arrival"></i>
                                                                                </div>
                                                                                <div class="start-time-info">
                                                                                    <h6 class="end-time-text">08:35</h6>
                                                                                    <p class="flight-full-date">Sat, 25 Oct, 2025</p>
                                                                                    <span class="flight-destination">LAX</span>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div class="tab-pane fade" id="fl-tab-pane10"
                                                                    role="tabpanel" aria-labelledby="fl-tab10"
                                                                    tabindex="0">
                                                                    <div class="flight-booking-detail-info">
                                                                        <div class="flight-booking-airline">
                                                                            <div class="flight-airline-img">
                                                                                <img src="assets/img/flight/airline-7.png" alt="">
                                                                            </div>
                                                                            <div class="flight-airline-info flex-grow-1">
                                                                                <h5 class="flight-airline-name">Delta Airline</h5>
                                                                                <span class="flight-airline-model">SG 143 | AT7</span>
                                                                            </div>
                                                                            <p class="flight-airline-class">( Economy )</p>
                                                                        </div>
                                                                        <div class="flight-booking-time mt-0 flight-booking-return">
                                                                            <div class="start-time">
                                                                                <div class="start-time-icon">
                                                                                    <i class="fal fa-plane-departure"></i>
                                                                                </div>
                                                                                <div class="start-time-info">
                                                                                    <h6 class="start-time-text">07:30</h6>
                                                                                    <p class="flight-full-date">Sat, 22 Oct, 2025</p>
                                                                                    <span class="flight-destination">JFK</span>
                                                                                </div>
                                                                            </div>
                                                                            <div class="flight-stop">
                                                                                <span class="flight-stop-number">Non Stop</span>
                                                                                <div class="flight-stop-arrow"></div>
                                                                                <div class="flight-booking-duration">
                                                                                    <span class="duration-text">4h 05m</span>
                                                                                </div>
                                                                            </div>
                                                                            <div class="end-time">
                                                                                <div class="start-time-icon">
                                                                                    <i class="fal fa-plane-arrival"></i>
                                                                                </div>
                                                                                <div class="start-time-info">
                                                                                    <h6 class="end-time-text">08:35</h6>
                                                                                    <p class="flight-full-date">Sat, 25 Oct, 2025</p>
                                                                                    <span class="flight-destination">LAX</span>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-12 col-xl-6">
                                                        <div class="flight-booking-detail-right">
                                                            <ul class="nav nav-tabs" id="frTab4" role="tablist">
                                                                <li class="nav-item" role="presentation">
                                                                    <button class="nav-link active" id="fr-tab10"
                                                                        data-bs-toggle="tab"
                                                                        data-bs-target="#fr-tab-pane10" type="button"
                                                                        role="tab" aria-controls="fr-tab-pane10"
                                                                        aria-selected="true">Baggage</button>
                                                                </li>
                                                                <li class="nav-item" role="presentation">
                                                                    <button class="nav-link" id="fr-tab11"
                                                                        data-bs-toggle="tab"
                                                                        data-bs-target="#fr-tab-pane11" type="button"
                                                                        role="tab" aria-controls="fr-tab-pane11"
                                                                        aria-selected="false">Fare</button>
                                                                </li>
                                                                <li class="nav-item" role="presentation">
                                                                    <button class="nav-link" id="fr-tab12"
                                                                        data-bs-toggle="tab"
                                                                        data-bs-target="#fr-tab-pane12" type="button"
                                                                        role="tab" aria-controls="fr-tab-pane12"
                                                                        aria-selected="false">Policy</button>
                                                                </li>
                                                            </ul>
                                                            <div class="tab-content" id="frTabContent4">
                                                                <div class="tab-pane fade show active"
                                                                    id="fr-tab-pane10" role="tabpanel"
                                                                    aria-labelledby="fr-tab10" tabindex="0">
                                                                    <div class="flight-booking-detail-info">
                                                                        <table class="table table-borderless">
                                                                            <tr>
                                                                                <th>Flight</th>
                                                                                <th>Cabin</th>
                                                                                <th>Check In</th>
                                                                            </tr>
                                                                            <tr>
                                                                                <td>JFK - LAX</td>
                                                                                <td>7 Kilograms</td>
                                                                                <td>20 Kilograms</td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td>LAX - JFK</td>
                                                                                <td>7 Kilograms</td>
                                                                                <td>20 Kilograms</td>
                                                                            </tr>
                                                                        </table>
                                                                    </div>
                                                                </div>
                                                                <div class="tab-pane fade" id="fr-tab-pane11"
                                                                    role="tabpanel" aria-labelledby="fr-tab11"
                                                                    tabindex="0">
                                                                    <div class="flight-booking-detail-info">
                                                                        <table class="table table-borderless">
                                                                            <tr>
                                                                                <th>Fare Summary</th>
                                                                                <th>Base Fare</th>
                                                                                <th>Tax</th>
                                                                            </tr>
                                                                            <tr>
                                                                                <td>Adult x 1</td>
                                                                                <td>$5,423</td>
                                                                                <td>$1,000</td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td>Child x 1</td>
                                                                                <td>$3,423</td>
                                                                                <td>$1,000</td>
                                                                            </tr>
                                                                        </table>
                                                                    </div>
                                                                </div>
                                                                <div class="tab-pane fade" id="fr-tab-pane12"
                                                                    role="tabpanel" aria-labelledby="fr-tab12"
                                                                    tabindex="0">
                                                                    <div class="flight-booking-detail-info">
                                                                        <div class="flight-booking-policy">
                                                                            <ul>
                                                                                <li>
                                                                                   1. Refund and Date Change are done as per the following policies.
                                                                                </li>
                                                                                <li>
                                                                                   2. Refund Amount= Refund Charge (as per airline policy + ShareTrip Convenience Fee).
                                                                                </li>
                                                                                <li>
                                                                                   3. Date Change Amount= Date Change Fee (as per Airline Policy + ShareTrip Convenience Fee).
                                                                                </li>
                                                                            </ul>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="flight-booking-detail-price">
                                                                <h6 class="flight-booking-detail-price-title">Total (2 Traveler)</h6>
                                                                <div class="flight-detail-price-amount">
                                                                    $10,846
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- flight booking item -->
                            <div class="col-lg-12">
                                <div class="flight-booking-item">
                                    <div class="flight-booking-wrapper">
                                        <div class="flight-booking-info">
                                            <div class="flight-booking-content">
                                                <div class="flight-booking-airline">
                                                    <div class="flight-airline-img">
                                                        <img src="assets/img/flight/airline-7.png" alt="">
                                                    </div>
                                                    <h5 class="flight-airline-name">Delta</h5>
                                                </div>
                                                <div class="flight-booking-time">
                                                    <div class="start-time">
                                                        <div class="start-time-icon">
                                                            <i class="fal fa-plane-departure"></i>
                                                        </div>
                                                        <div class="start-time-info">
                                                            <h6 class="start-time-text">07:30</h6>
                                                            <span class="flight-destination">JFK</span>
                                                        </div>
                                                    </div>
                                                    <div class="flight-stop">
                                                        <span class="flight-stop-number">Non Stop</span>
                                                        <div class="flight-stop-arrow"></div>
                                                    </div>
                                                    <div class="end-time">
                                                        <div class="start-time-icon">
                                                            <i class="fal fa-plane-arrival"></i>
                                                        </div>
                                                        <div class="start-time-info">
                                                            <h6 class="end-time-text">08:35</h6>
                                                            <span class="flight-destination">LAX</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="flight-booking-duration">
                                                    <span class="duration-text">4h 05m</span>
                                                </div>
                                            </div>
                                            <div class="flight-booking-content flight-booking-return">
                                                <div class="flight-booking-airline">
                                                    <div class="flight-airline-img">
                                                        <img src="assets/img/flight/airline-7.png" alt="">
                                                    </div>
                                                    <h5 class="flight-airline-name">Delta</h5>
                                                </div>
                                                <div class="flight-booking-time">
                                                    <div class="start-time">
                                                        <div class="start-time-icon">
                                                            <i class="fal fa-plane-departure"></i>
                                                        </div>
                                                        <div class="start-time-info">
                                                            <h6 class="start-time-text">07:30</h6>
                                                            <span class="flight-destination">JFK</span>
                                                        </div>
                                                    </div>
                                                    <div class="flight-stop">
                                                        <span class="flight-stop-number">Non Stop</span>
                                                        <div class="flight-stop-arrow"></div>
                                                    </div>
                                                    <div class="end-time">
                                                        <div class="start-time-icon">
                                                            <i class="fal fa-plane-arrival"></i>
                                                        </div>
                                                        <div class="start-time-info">
                                                            <h6 class="end-time-text">08:35</h6>
                                                            <span class="flight-destination">LAX</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="flight-booking-duration">
                                                    <span class="duration-text">4h 05m</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flight-booking-price">
                                            <div class="price-info">
                                                <span class="price-amount">$4,548</span>
                                            </div>
                                            <a href="#" class="theme-btn">Book Now<i class="fas fa-arrow-circle-right"></i></a>
                                        </div>
                                    </div>
                                    <div class="flight-booking-detail">
                                        <div class="flight-booking-detail-header">
                                            <p>Partially Refundable</p>
                                            <a href="#flight-booking-collapse5" data-bs-toggle="collapse" role="button"
                                                aria-expanded="false" aria-controls="flight-booking-collapse5">Flight
                                                Details <i class="far fa-angle-down"></i></a>
                                        </div>
                                        <div class="collapse" id="flight-booking-collapse5">
                                            <div class="flight-booking-detail-wrapper">
                                                <div class="row">
                                                    <div class="col-lg-12 col-xl-6">
                                                        <div class="flight-booking-detail-left">
                                                            <ul class="nav nav-tabs" id="flTab5" role="tablist">
                                                                <li class="nav-item" role="presentation">
                                                                    <button class="nav-link active" id="fl-tab11"
                                                                        data-bs-toggle="tab"
                                                                        data-bs-target="#fl-tab-pane11" type="button"
                                                                        role="tab" aria-controls="fl-tab-pane11"
                                                                        aria-selected="true">JFK - LAX</button>
                                                                </li>
                                                                <li class="nav-item" role="presentation">
                                                                    <button class="nav-link" id="fl-tab12"
                                                                        data-bs-toggle="tab"
                                                                        data-bs-target="#fl-tab-pane12" type="button"
                                                                        role="tab" aria-controls="fl-tab-pane12"
                                                                        aria-selected="false">LAX - JFK</button>
                                                                </li>
                                                            </ul>
                                                            <div class="tab-content" id="flTabContent5">
                                                                <div class="tab-pane fade show active"
                                                                    id="fl-tab-pane11" role="tabpanel"
                                                                    aria-labelledby="fl-tab11" tabindex="0">
                                                                    <div class="flight-booking-detail-info">
                                                                        <div class="flight-booking-airline">
                                                                            <div class="flight-airline-img">
                                                                                <img src="assets/img/flight/airline-7.png" alt="">
                                                                            </div>
                                                                            <div class="flight-airline-info flex-grow-1">
                                                                                <h5 class="flight-airline-name">Delta Airline</h5>
                                                                                <span class="flight-airline-model">SG 143 | AT7</span>
                                                                            </div>
                                                                            <p class="flight-airline-class">( Economy )</p>
                                                                        </div>
                                                                        <div class="flight-booking-time">
                                                                            <div class="start-time">
                                                                                <div class="start-time-icon">
                                                                                    <i class="fal fa-plane-departure"></i>
                                                                                </div>
                                                                                <div class="start-time-info">
                                                                                    <h6 class="start-time-text">07:30</h6>
                                                                                    <p class="flight-full-date">Sat, 22 Oct, 2025</p>
                                                                                    <span class="flight-destination">JFK</span>
                                                                                </div>
                                                                            </div>
                                                                            <div class="flight-stop">
                                                                                <span class="flight-stop-number">Non Stop</span>
                                                                                <div class="flight-stop-arrow"></div>
                                                                                <div class="flight-booking-duration">
                                                                                    <span class="duration-text">4h 05m</span>
                                                                                </div>
                                                                            </div>
                                                                            <div class="end-time">
                                                                                <div class="start-time-icon">
                                                                                    <i class="fal fa-plane-arrival"></i>
                                                                                </div>
                                                                                <div class="start-time-info">
                                                                                    <h6 class="end-time-text">08:35</h6>
                                                                                    <p class="flight-full-date">Sat, 25 Oct, 2025</p>
                                                                                    <span class="flight-destination">LAX</span>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div class="tab-pane fade" id="fl-tab-pane12"
                                                                    role="tabpanel" aria-labelledby="fl-tab12"
                                                                    tabindex="0">
                                                                    <div class="flight-booking-detail-info">
                                                                        <div class="flight-booking-airline">
                                                                            <div class="flight-airline-img">
                                                                                <img src="assets/img/flight/airline-7.png" alt="">
                                                                            </div>
                                                                            <div class="flight-airline-info flex-grow-1">
                                                                                <h5 class="flight-airline-name">Delta Airline</h5>
                                                                                <span class="flight-airline-model">SG 143 | AT7</span>
                                                                            </div>
                                                                            <p class="flight-airline-class">( Economy )</p>
                                                                        </div>
                                                                        <div class="flight-booking-time mt-0 flight-booking-return">
                                                                            <div class="start-time">
                                                                                <div class="start-time-icon">
                                                                                    <i class="fal fa-plane-departure"></i>
                                                                                </div>
                                                                                <div class="start-time-info">
                                                                                    <h6 class="start-time-text">07:30</h6>
                                                                                    <p class="flight-full-date">Sat, 22 Oct, 2025</p>
                                                                                    <span class="flight-destination">JFK</span>
                                                                                </div>
                                                                            </div>
                                                                            <div class="flight-stop">
                                                                                <span class="flight-stop-number">Non Stop</span>
                                                                                <div class="flight-stop-arrow"></div>
                                                                                <div class="flight-booking-duration">
                                                                                    <span class="duration-text">4h 05m</span>
                                                                                </div>
                                                                            </div>
                                                                            <div class="end-time">
                                                                                <div class="start-time-icon">
                                                                                    <i class="fal fa-plane-arrival"></i>
                                                                                </div>
                                                                                <div class="start-time-info">
                                                                                    <h6 class="end-time-text">08:35</h6>
                                                                                    <p class="flight-full-date">Sat, 25 Oct, 2025</p>
                                                                                    <span class="flight-destination">LAX</span>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-12 col-xl-6">
                                                        <div class="flight-booking-detail-right">
                                                            <ul class="nav nav-tabs" id="frTab5" role="tablist">
                                                                <li class="nav-item" role="presentation">
                                                                    <button class="nav-link active" id="fr-tab13"
                                                                        data-bs-toggle="tab"
                                                                        data-bs-target="#fr-tab-pane13" type="button"
                                                                        role="tab" aria-controls="fr-tab-pane13"
                                                                        aria-selected="true">Baggage</button>
                                                                </li>
                                                                <li class="nav-item" role="presentation">
                                                                    <button class="nav-link" id="fr-tab14"
                                                                        data-bs-toggle="tab"
                                                                        data-bs-target="#fr-tab-pane14" type="button"
                                                                        role="tab" aria-controls="fr-tab-pane14"
                                                                        aria-selected="false">Fare</button>
                                                                </li>
                                                                <li class="nav-item" role="presentation">
                                                                    <button class="nav-link" id="fr-tab15"
                                                                        data-bs-toggle="tab"
                                                                        data-bs-target="#fr-tab-pane15" type="button"
                                                                        role="tab" aria-controls="fr-tab-pane15"
                                                                        aria-selected="false">Policy</button>
                                                                </li>
                                                            </ul>
                                                            <div class="tab-content" id="frTabContent5">
                                                                <div class="tab-pane fade show active"
                                                                    id="fr-tab-pane13" role="tabpanel"
                                                                    aria-labelledby="fr-tab13" tabindex="0">
                                                                    <div class="flight-booking-detail-info">
                                                                        <table class="table table-borderless">
                                                                            <tr>
                                                                                <th>Flight</th>
                                                                                <th>Cabin</th>
                                                                                <th>Check In</th>
                                                                            </tr>
                                                                            <tr>
                                                                                <td>JFK - LAX</td>
                                                                                <td>7 Kilograms</td>
                                                                                <td>20 Kilograms</td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td>LAX - JFK</td>
                                                                                <td>7 Kilograms</td>
                                                                                <td>20 Kilograms</td>
                                                                            </tr>
                                                                        </table>
                                                                    </div>
                                                                </div>
                                                                <div class="tab-pane fade" id="fr-tab-pane14"
                                                                    role="tabpanel" aria-labelledby="fr-tab14"
                                                                    tabindex="0">
                                                                    <div class="flight-booking-detail-info">
                                                                        <table class="table table-borderless">
                                                                            <tr>
                                                                                <th>Fare Summary</th>
                                                                                <th>Base Fare</th>
                                                                                <th>Tax</th>
                                                                            </tr>
                                                                            <tr>
                                                                                <td>Adult x 1</td>
                                                                                <td>$5,423</td>
                                                                                <td>$1,000</td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td>Child x 1</td>
                                                                                <td>$3,423</td>
                                                                                <td>$1,000</td>
                                                                            </tr>
                                                                        </table>
                                                                    </div>
                                                                </div>
                                                                <div class="tab-pane fade" id="fr-tab-pane15"
                                                                    role="tabpanel" aria-labelledby="fr-tab15"
                                                                    tabindex="0">
                                                                    <div class="flight-booking-detail-info">
                                                                        <div class="flight-booking-policy">
                                                                            <ul>
                                                                                <li>
                                                                                   1. Refund and Date Change are done as per the following policies.
                                                                                </li>
                                                                                <li>
                                                                                   2. Refund Amount= Refund Charge (as per airline policy + ShareTrip Convenience Fee).
                                                                                </li>
                                                                                <li>
                                                                                   3. Date Change Amount= Date Change Fee (as per Airline Policy + ShareTrip Convenience Fee).
                                                                                </li>
                                                                            </ul>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="flight-booking-detail-price">
                                                                <h6 class="flight-booking-detail-price-title">Total (2 Traveler)</h6>
                                                                <div class="flight-detail-price-amount">
                                                                    $10,846
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- flight booking item -->
                            <div class="col-lg-12">
                                <div class="flight-booking-item">
                                    <div class="flight-booking-wrapper">
                                        <div class="flight-booking-info">
                                            <div class="flight-booking-content">
                                                <div class="flight-booking-airline">
                                                    <div class="flight-airline-img">
                                                        <img src="assets/img/flight/airline-7.png" alt="">
                                                    </div>
                                                    <h5 class="flight-airline-name">Delta</h5>
                                                </div>
                                                <div class="flight-booking-time">
                                                    <div class="start-time">
                                                        <div class="start-time-icon">
                                                            <i class="fal fa-plane-departure"></i>
                                                        </div>
                                                        <div class="start-time-info">
                                                            <h6 class="start-time-text">07:30</h6>
                                                            <span class="flight-destination">JFK</span>
                                                        </div>
                                                    </div>
                                                    <div class="flight-stop">
                                                        <span class="flight-stop-number">Non Stop</span>
                                                        <div class="flight-stop-arrow"></div>
                                                    </div>
                                                    <div class="end-time">
                                                        <div class="start-time-icon">
                                                            <i class="fal fa-plane-arrival"></i>
                                                        </div>
                                                        <div class="start-time-info">
                                                            <h6 class="end-time-text">08:35</h6>
                                                            <span class="flight-destination">LAX</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="flight-booking-duration">
                                                    <span class="duration-text">4h 05m</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flight-booking-price">
                                            <div class="price-info">
                                                <span class="price-amount">$4,548</span>
                                            </div>
                                            <a href="#" class="theme-btn">Book Now<i class="fas fa-arrow-circle-right"></i></a>
                                        </div>
                                    </div>
                                    <div class="flight-booking-detail">
                                        <div class="flight-booking-detail-header">
                                            <p>Partially Refundable</p>
                                            <a href="#flight-booking-collapse6" data-bs-toggle="collapse" role="button"
                                                aria-expanded="false" aria-controls="flight-booking-collapse6">Flight
                                                Details <i class="far fa-angle-down"></i></a>
                                        </div>
                                        <div class="collapse" id="flight-booking-collapse6">
                                            <div class="flight-booking-detail-wrapper">
                                                <div class="row">
                                                    <div class="col-lg-12 col-xl-6">
                                                        <div class="flight-booking-detail-left">
                                                            <ul class="nav nav-tabs" id="flTab6" role="tablist">
                                                                <li class="nav-item" role="presentation">
                                                                    <button class="nav-link active" id="fl-tab13"
                                                                        data-bs-toggle="tab"
                                                                        data-bs-target="#fl-tab-pane13" type="button"
                                                                        role="tab" aria-controls="fl-tab-pane13"
                                                                        aria-selected="true">JFK - LAX</button>
                                                                </li>
                                                            </ul>
                                                            <div class="tab-content" id="flTabContent6">
                                                                <div class="tab-pane fade show active"
                                                                    id="fl-tab-pane13" role="tabpanel"
                                                                    aria-labelledby="fl-tab13" tabindex="0">
                                                                    <div class="flight-booking-detail-info">
                                                                        <div class="flight-booking-airline">
                                                                            <div class="flight-airline-img">
                                                                                <img src="assets/img/flight/airline-7.png" alt="">
                                                                            </div>
                                                                            <div class="flight-airline-info flex-grow-1">
                                                                                <h5 class="flight-airline-name">Delta Airline</h5>
                                                                                <span class="flight-airline-model">SG 143 | AT7</span>
                                                                            </div>
                                                                            <p class="flight-airline-class">( Economy )</p>
                                                                        </div>
                                                                        <div class="flight-booking-time">
                                                                            <div class="start-time">
                                                                                <div class="start-time-icon">
                                                                                    <i class="fal fa-plane-departure"></i>
                                                                                </div>
                                                                                <div class="start-time-info">
                                                                                    <h6 class="start-time-text">07:30</h6>
                                                                                    <p class="flight-full-date">Sat, 22 Oct, 2025</p>
                                                                                    <span class="flight-destination">JFK</span>
                                                                                </div>
                                                                            </div>
                                                                            <div class="flight-stop">
                                                                                <span class="flight-stop-number">Non Stop</span>
                                                                                <div class="flight-stop-arrow"></div>
                                                                                <div class="flight-booking-duration">
                                                                                    <span class="duration-text">4h 05m</span>
                                                                                </div>
                                                                            </div>
                                                                            <div class="end-time">
                                                                                <div class="start-time-icon">
                                                                                    <i class="fal fa-plane-arrival"></i>
                                                                                </div>
                                                                                <div class="start-time-info">
                                                                                    <h6 class="end-time-text">08:35</h6>
                                                                                    <p class="flight-full-date">Sat, 25 Oct, 2025</p>
                                                                                    <span class="flight-destination">LAX</span>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-12 col-xl-6">
                                                        <div class="flight-booking-detail-right">
                                                            <ul class="nav nav-tabs" id="frTab6" role="tablist">
                                                                <li class="nav-item" role="presentation">
                                                                    <button class="nav-link active" id="fr-tab16"
                                                                        data-bs-toggle="tab"
                                                                        data-bs-target="#fr-tab-pane16" type="button"
                                                                        role="tab" aria-controls="fr-tab-pane16"
                                                                        aria-selected="true">Baggage</button>
                                                                </li>
                                                                <li class="nav-item" role="presentation">
                                                                    <button class="nav-link" id="fr-tab17"
                                                                        data-bs-toggle="tab"
                                                                        data-bs-target="#fr-tab-pane17" type="button"
                                                                        role="tab" aria-controls="fr-tab-pane17"
                                                                        aria-selected="false">Fare</button>
                                                                </li>
                                                                <li class="nav-item" role="presentation">
                                                                    <button class="nav-link" id="fr-tab18"
                                                                        data-bs-toggle="tab"
                                                                        data-bs-target="#fr-tab-pane18" type="button"
                                                                        role="tab" aria-controls="fr-tab-pane18"
                                                                        aria-selected="false">Policy</button>
                                                                </li>
                                                            </ul>
                                                            <div class="tab-content" id="frTabContent6">
                                                                <div class="tab-pane fade show active"
                                                                    id="fr-tab-pane16" role="tabpanel"
                                                                    aria-labelledby="fr-tab16" tabindex="0">
                                                                    <div class="flight-booking-detail-info">
                                                                        <table class="table table-borderless">
                                                                            <tr>
                                                                                <th>Flight</th>
                                                                                <th>Cabin</th>
                                                                                <th>Check In</th>
                                                                            </tr>
                                                                            <tr>
                                                                                <td>JFK - LAX</td>
                                                                                <td>7 Kilograms</td>
                                                                                <td>20 Kilograms</td>
                                                                            </tr>
                                                                        </table>
                                                                    </div>
                                                                </div>
                                                                <div class="tab-pane fade" id="fr-tab-pane17"
                                                                    role="tabpanel" aria-labelledby="fr-tab17"
                                                                    tabindex="0">
                                                                    <div class="flight-booking-detail-info">
                                                                        <table class="table table-borderless">
                                                                            <tr>
                                                                                <th>Fare Summary</th>
                                                                                <th>Base Fare</th>
                                                                                <th>Tax</th>
                                                                            </tr>
                                                                            <tr>
                                                                                <td>Adult x 1</td>
                                                                                <td>$5,423</td>
                                                                                <td>$1,000</td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td>Child x 1</td>
                                                                                <td>$3,423</td>
                                                                                <td>$1,000</td>
                                                                            </tr>
                                                                        </table>
                                                                    </div>
                                                                </div>
                                                                <div class="tab-pane fade" id="fr-tab-pane18"
                                                                    role="tabpanel" aria-labelledby="fr-tab18"
                                                                    tabindex="0">
                                                                    <div class="flight-booking-detail-info">
                                                                        <div class="flight-booking-policy">
                                                                            <ul>
                                                                                <li>
                                                                                   1. Refund and Date Change are done as per the following policies.
                                                                                </li>
                                                                                <li>
                                                                                   2. Refund Amount= Refund Charge (as per airline policy + ShareTrip Convenience Fee).
                                                                                </li>
                                                                                <li>
                                                                                   3. Date Change Amount= Date Change Fee (as per Airline Policy + ShareTrip Convenience Fee).
                                                                                </li>
                                                                            </ul>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="flight-booking-detail-price">
                                                                <h6 class="flight-booking-detail-price-title">Total (2 Traveler)</h6>
                                                                <div class="flight-detail-price-amount">
                                                                    $10,846
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- pagination -->
                            <div class="pagination-area">
                                <div aria-label="Page navigation example">
                                    <ul class="pagination">
                                        <li class="page-item">
                                            <a class="page-link" href="#" aria-label="Previous">
                                                <span aria-hidden="true"><i class="far fa-angle-double-left"></i></span>
                                            </a>
                                        </li>
                                        <li class="page-item active"><a class="page-link" href="#">1</a></li>
                                        <li class="page-item"><a class="page-link" href="#">2</a></li>
                                        <li class="page-item"><a class="page-link" href="#">3</a></li>
                                        <li class="page-item">
                                            <a class="page-link" href="#" aria-label="Next">
                                                <span aria-hidden="true"><i
                                                        class="far fa-angle-double-right"></i></span>
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                                <div class="pagination-showing"> 
                                    <p>Showing 1 - 6 of 24 Flights</p>
                                </div>
                            </div>
                            <!-- pagination end -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- flight booking end -->

    </main>


    <!-- footer area -->
    <footer class="footer-area ft-bg">
        <div class="footer-widget">
            <div class="container">
                <div class="row footer-widget-wrapper pt-100 pb-70">
                    <div class="col-md-6 col-lg-3">
                        <div class="footer-widget-box about-us">
                            <a href="#" class="footer-logo">
                                <img src="assets/img/logo/logo.png" alt="">
                            </a>
                            <p class="mb-4">
                                We are many variations of passages available but the majority have suffer alteration
                                in some form by injected.
                            </p>
                            <ul class="footer-contact">
                                <li>
                                    <div class="footer-call">
                                        <div class="footer-call-icon">
                                            <i class="fal fa-headset"></i>
                                        </div>
                                        <div class="footer-call-info">
                                            <h6>24/7 Call Service</h6>
                                            <a href="tel:+21236547898">****** 654 7898</a>
                                        </div>
                                    </div>
                                </li>
                                <li><i class="far fa-map-marker-alt"></i>25/B Milford Road, New York</li>
                                <li><a href="https://live.themewild.com/cdn-cgi/l/email-protection#e1888f878ea18499808c918d84cf828e8c"><i
                                            class="far fa-envelopes"></i><span class="__cf_email__" data-cfemail="d3babdb5bc93b6abb2bea3bfb6fdb0bcbe">[email&#160;protected]</span></a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-2">
                        <div class="footer-widget-box list">
                            <h4 class="footer-widget-title">Our Company</h4>
                            <ul class="footer-list">
                                <li><a href="#"><i class="fas fa-angle-double-right"></i> About Us</a></li>
                                <li><a href="#"><i class="fas fa-angle-double-right"></i> Meet Our Team</a></li>
                                <li><a href="#"><i class="fas fa-angle-double-right"></i> Contact Us</a></li>
                                <li><a href="#"><i class="fas fa-angle-double-right"></i> Affiliate Program</a></li>
                                <li><a href="#"><i class="fas fa-angle-double-right"></i> Advertising With Us</a></li>
                                <li><a href="#"><i class="fas fa-angle-double-right"></i> Careers</a></li>
                                <li><a href="#"><i class="fas fa-angle-double-right"></i> Our Blog</a></li>

                            </ul>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-2">
                        <div class="footer-widget-box list">
                            <h4 class="footer-widget-title">Other Services</h4>
                            <ul class="footer-list">
                                <li><a href="#"><i class="fas fa-angle-double-right"></i> Rewards Program</a></li>
                                <li><a href="#"><i class="fas fa-angle-double-right"></i> Partners</a></li>
                                <li><a href="#"><i class="fas fa-angle-double-right"></i> Community Program</a></li>
                                <li><a href="#"><i class="fas fa-angle-double-right"></i> Investor Relations</a></li>
                                <li><a href="#"><i class="fas fa-angle-double-right"></i> Developer Guide</a></li>
                                <li><a href="#"><i class="fas fa-angle-double-right"></i> Travel API</a></li>
                                <li><a href="#"><i class="fas fa-angle-double-right"></i> PointsPLUS</a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-2">
                        <div class="footer-widget-box list">
                            <h4 class="footer-widget-title">Help Center</h4>
                            <ul class="footer-list">
                                <li><a href="#"><i class="fas fa-angle-double-right"></i> Account</a></li>
                                <li><a href="#"><i class="fas fa-angle-double-right"></i> FAQ's</a></li>
                                <li><a href="#"><i class="fas fa-angle-double-right"></i> Legal Notice</a></li>
                                <li><a href="#"><i class="fas fa-angle-double-right"></i> Privacy Policy</a></li>
                                <li><a href="#"><i class="fas fa-angle-double-right"></i> Terms & Conditions</a></li>
                                <li><a href="#"><i class="fas fa-angle-double-right"></i> Live Chat</a></li>
                                <li><a href="#"><i class="fas fa-angle-double-right"></i> Sitemap</a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-3">
                        <div class="footer-widget-box list">
                            <h4 class="footer-widget-title">Newsletter</h4>
                            <div class="footer-newsletter">
                                <p>Subscribe Our Newsletter To Get Latest Update And News</p>
                                <div class="subscribe-form">
                                    <form action="#">
                                        <div class="form-group">
                                            <div class="form-group-icon">
                                                <input type="email" class="form-control" placeholder="Your Email">
                                                <i class="far fa-envelopes"></i>
                                            </div>
                                        </div>
                                        <button class="theme-btn" type="submit">
                                            Subscribe Now <i class="far fa-paper-plane"></i>
                                        </button>
                                        <p><i class="far fa-lock"></i> Your information is safe with us.</p>
                                    </form>
                                </div>
                            </div>
                            <div class="footer-payment-method">
                                <h6>We Accept:</h6>
                                <div class="payment-method-img">
                                    <img src="assets/img/payment/paypal.svg" alt="">
                                    <img src="assets/img/payment/mastercard.svg" alt="">
                                    <img src="assets/img/payment/visa.svg" alt="">
                                    <img src="assets/img/payment/discover.svg" alt="">
                                    <img src="assets/img/payment/american-express.svg" alt="">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="copyright">
            <div class="container">
                <div class="row">
                    <div class="col-md-6 align-self-center">
                        <p class="copyright-text">
                            &copy; Copyright <span id="date"></span> <a href="#"> Tavelo </a> All Rights Reserved.
                        </p>
                    </div>
                    <div class="col-md-6 align-self-center">
                        <ul class="footer-social">
                            <li><a href="#"><i class="fab fa-facebook-f"></i></a></li>
                            <li><a href="#"><i class="fab fa-x-twitter"></i></a></li>
                            <li><a href="#"><i class="fab fa-linkedin-in"></i></a></li>
                            <li><a href="#"><i class="fab fa-youtube"></i></a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    <!-- footer area end -->


    <!-- scroll-top -->
    <a href="#" id="scroll-top"><i class="far fa-angle-up"></i></a>
    <!-- scroll-top end -->


    <!-- js -->
    <script data-cfasync="false" src="../cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script><script src="assets/js/jquery-3.7.1.min.js"></script>
    <script src="assets/js/modernizr.min.js"></script>
    <script src="assets/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/imagesloaded.pkgd.min.js"></script>
    <script src="assets/js/jquery.magnific-popup.min.js"></script>
    <script src="assets/js/isotope.pkgd.min.js"></script>
    <script src="assets/js/jquery.appear.min.js"></script>
    <script src="assets/js/jquery.easing.min.js"></script>
    <script src="assets/js/owl.carousel.min.js"></script>
    <script src="assets/js/counter-up.js"></script>
    <script src="assets/js/masonry.pkgd.min.js"></script>
    <script src="assets/js/jquery.nice-select.min.js"></script>
    <script src="assets/js/jquery-ui.min.js"></script>
    <script src="assets/js/jquery.timepicker.min.js"></script>
    <script src="assets/js/wow.min.js"></script>
    <script src="assets/js/main.js"></script>

</body>


<!-- Mirrored from live.themewild.com/tavelo/flight-search-result.html by HTTrack Website Copier/3.x [XR&CO'2014], Tue, 02 Sep 2025 05:11:29 GMT -->
</html>