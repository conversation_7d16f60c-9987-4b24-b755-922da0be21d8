(function(){/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var m;function aa(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}}function ba(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("Cannot find global object");}var ca=ba(this);
function p(a){var b=typeof Symbol!="undefined"&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:aa(a)};throw Error(String(a)+" is not an iterable or ArrayLike");}function q(a){if(!(a instanceof Array)){a=p(a);for(var b,c=[];!(b=a.next()).done;)c.push(b.value);a=c}return a}function r(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};/*

 Copyright (c) 2016 The Polymer Project Authors. All rights reserved.
 This code may only be used under the BSD style license found at
 http://polymer.github.io/LICENSE.txt The complete set of authors may be found
 at http://polymer.github.io/AUTHORS.txt The complete set of contributors may
 be found at http://polymer.github.io/CONTRIBUTORS.txt Code distributed by
 Google as part of the polymer project is also subject to an additional IP
 rights grant found at http://polymer.github.io/PATENTS.txt
*/
Array.from||(Array.from=function(a){return[].slice.call(a)});Object.assign||(Object.assign=function(a){for(var b=[].slice.call(arguments,1),c=0,d;c<b.length;c++)if(d=b[c])for(var e=a,f=Object.keys(d),g=0;g<f.length;g++){var h=f[g];e[h]=d[h]}return a});/*


 Copyright (c) 2014 Taylor Hakes
 Copyright (c) 2014 Forbes Lindesay

 Permission is hereby granted, free of charge, to any person obtaining a copy
 of this software and associated documentation files (the "Software"), to deal
 in the Software without restriction, including without limitation the rights
 to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 copies of the Software, and to permit persons to whom the Software is
 furnished to do so, subject to the following conditions:

 The above copyright notice and this permission notice shall be included in
 all copies or substantial portions of the Software.

 THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 THE SOFTWARE.
*/
var da=setTimeout;function ea(){}function fa(a,b){return function(){a.apply(b,arguments)}}function u(a){if(!(this instanceof u))throw new TypeError("Promises must be constructed via new");if(typeof a!=="function")throw new TypeError("not a function");this.D=0;this.Qa=!1;this.u=void 0;this.U=[];ha(a,this)}
function ia(a,b){for(;a.D===3;)a=a.u;a.D===0?a.U.push(b):(a.Qa=!0,ja(function(){var c=a.D===1?b.xc:b.yc;if(c===null)(a.D===1?ka:la)(b.promise,a.u);else{try{var d=c(a.u)}catch(e){la(b.promise,e);return}ka(b.promise,d)}}))}
function ka(a,b){try{if(b===a)throw new TypeError("A promise cannot be resolved with itself.");if(b&&(typeof b==="object"||typeof b==="function")){var c=b.then;if(b instanceof u){a.D=3;a.u=b;ma(a);return}if(typeof c==="function"){ha(fa(c,b),a);return}}a.D=1;a.u=b;ma(a)}catch(d){la(a,d)}}function la(a,b){a.D=2;a.u=b;ma(a)}
function ma(a){a.D===2&&a.U.length===0&&ja(function(){a.Qa||typeof console!=="undefined"&&console&&console.warn("Possible Unhandled Promise Rejection:",a.u)});for(var b=0,c=a.U.length;b<c;b++)ia(a,a.U[b]);a.U=null}function na(a,b,c){this.xc=typeof a==="function"?a:null;this.yc=typeof b==="function"?b:null;this.promise=c}function ha(a,b){var c=!1;try{a(function(d){c||(c=!0,ka(b,d))},function(d){c||(c=!0,la(b,d))})}catch(d){c||(c=!0,la(b,d))}}u.prototype["catch"]=function(a){return this.then(null,a)};
u.prototype.then=function(a,b){var c=new this.constructor(ea);ia(this,new na(a,b,c));return c};u.prototype["finally"]=function(a){var b=this.constructor;return this.then(function(c){return b.resolve(a()).then(function(){return c})},function(c){return b.resolve(a()).then(function(){return b.reject(c)})})};
function oa(a){return new u(function(b,c){function d(h,k){try{if(k&&(typeof k==="object"||typeof k==="function")){var l=k.then;if(typeof l==="function"){l.call(k,function(n){d(h,n)},c);return}}e[h]=k;--f===0&&b(e)}catch(n){c(n)}}if(!a||typeof a.length==="undefined")throw new TypeError("Promise.all accepts an array");var e=Array.prototype.slice.call(a);if(e.length===0)return b([]);for(var f=e.length,g=0;g<e.length;g++)d(g,e[g])})}
function pa(a){return a&&typeof a==="object"&&a.constructor===u?a:new u(function(b){b(a)})}function qa(a){return new u(function(b,c){c(a)})}function ra(a){return new u(function(b,c){for(var d=0,e=a.length;d<e;d++)a[d].then(b,c)})}var ja=typeof setImmediate==="function"&&function(a){setImmediate(a)}||function(a){da(a,0)};/*

 Copyright (c) 2017 The Polymer Project Authors. All rights reserved.
 This code may only be used under the BSD style license found at
 http://polymer.github.io/LICENSE.txt The complete set of authors may be found at
 http://polymer.github.io/AUTHORS.txt The complete set of contributors may be
 found at http://polymer.github.io/CONTRIBUTORS.txt Code distributed by Google as
 part of the polymer project is also subject to an additional IP rights grant
 found at http://polymer.github.io/PATENTS.txt
*/
if(!window.Promise){window.Promise=u;u.prototype.then=u.prototype.then;u.all=oa;u.race=ra;u.resolve=pa;u.reject=qa;var sa=document.createTextNode(""),ta=[];(new MutationObserver(function(){for(var a=ta.length,b=0;b<a;b++)ta[b]();ta.splice(0,a)})).observe(sa,{characterData:!0});ja=function(a){ta.push(a);sa.textContent=sa.textContent.length>0?"":"a"}};var ua=document.createEvent("Event");ua.initEvent("foo",!0,!0);ua.preventDefault();if(!ua.defaultPrevented){var va=Event.prototype.preventDefault;Event.prototype.preventDefault=function(){this.cancelable&&(va.call(this),Object.defineProperty(this,"defaultPrevented",{get:function(){return!0},configurable:!0}))}}var xa=/Trident/.test(navigator.userAgent);
if(!window.Event||xa&&typeof window.Event!=="function"){var ya=window.Event;window.Event=function(a,b){b=b||{};var c=document.createEvent("Event");c.initEvent(a,!!b.bubbles,!!b.cancelable);return c};if(ya){for(var za in ya)window.Event[za]=ya[za];window.Event.prototype=ya.prototype}}
if(!window.CustomEvent||xa&&typeof window.CustomEvent!=="function")window.CustomEvent=function(a,b){b=b||{};var c=document.createEvent("CustomEvent");c.initCustomEvent(a,!!b.bubbles,!!b.cancelable,b.detail);return c},window.CustomEvent.prototype=window.Event.prototype;
if(!window.MouseEvent||xa&&typeof window.MouseEvent!=="function"){var Aa=window.MouseEvent;window.MouseEvent=function(a,b){b=b||{};var c=document.createEvent("MouseEvent");c.initMouseEvent(a,!!b.bubbles,!!b.cancelable,b.view||window,b.detail,b.screenX,b.screenY,b.clientX,b.clientY,b.ctrlKey,b.altKey,b.shiftKey,b.metaKey,b.button,b.relatedTarget);return c};if(Aa)for(var Ba in Aa)window.MouseEvent[Ba]=Aa[Ba];window.MouseEvent.prototype=Aa.prototype};var Ca=function(){function a(){e++}var b=!1,c=!1,d={get capture(){return b=!0},get once(){return c=!0}},e=0,f=document.createElement("div");f.addEventListener("click",a,d);var g=b&&c;g&&(f.dispatchEvent(new Event("click")),f.dispatchEvent(new Event("click")),g=e==1);f.removeEventListener("click",a,d);return g}(),Da,Ea=(Da=window.EventTarget)!=null?Da:window.Node;
if(!Ca&&"addEventListener"in Ea.prototype){var Fa=function(a){if(!a||typeof a!=="object"&&typeof a!=="function"){var b=!!a;a=!1}else b=!!a.capture,a=!!a.once;return{capture:b,once:a}},Ga=Ea.prototype.addEventListener,Ha=Ea.prototype.removeEventListener,Ia=new WeakMap,Ja=new WeakMap,Ka=function(a,b,c){var d=c?Ia:Ja;c=d.get(a);c===void 0&&d.set(a,c=new Map);a=c.get(b);a===void 0&&c.set(b,a=new WeakMap);return a};Ea.prototype.addEventListener=function(a,b,c){var d=this;if(b!=null){c=Fa(c);var e=c.capture;
c=c.once;var f=Ka(this,a,e);if(!f.has(b)){var g=c?function(k){f.delete(b);Ha.call(d,a,g,e);if(typeof b==="function")return b.call(d,k);if(typeof(b==null?void 0:b.handleEvent)==="function")return b.handleEvent(k)}:null;f.set(b,g);var h;Ga.call(this,a,(h=g)!=null?h:b,e)}}};Ea.prototype.removeEventListener=function(a,b,c){if(b!=null){c=Fa(c).capture;var d=Ka(this,a,c),e=d.get(b);e!==void 0&&(d.delete(b),Ha.call(this,a,e!=null?e:b,c))}}};/*

 Copyright (c) 2020 The Polymer Project Authors. All rights reserved.
 This code may only be used under the BSD style license found at
 http://polymer.github.io/LICENSE.txt The complete set of authors may be found at
 http://polymer.github.io/AUTHORS.txt The complete set of contributors may be
 found at http://polymer.github.io/CONTRIBUTORS.txt Code distributed by Google as
 part of the polymer project is also subject to an additional IP rights grant
 found at http://polymer.github.io/PATENTS.txt
*/
var La=Element.prototype,Ma,Na,Oa,Pa=(Oa=(Na=(Ma=Object.getOwnPropertyDescriptor(La,"attributes"))!=null?Ma:Object.getOwnPropertyDescriptor(Node.prototype,"attributes"))==null?void 0:Na.get)!=null?Oa:function(){return this.attributes},Qa=Array.prototype.map;La.hasOwnProperty("getAttributeNames")||(La.getAttributeNames=function(){return Qa.call(Pa.call(this),function(a){return a.name})});var Ra=Element.prototype;if(!Ra.hasOwnProperty("matches")){var Sa;Ra.matches=(Sa=Ra.webkitMatchesSelector)!=null?Sa:Ra.msMatchesSelector};/*

 Copyright (c) 2020 The Polymer Project Authors. All rights reserved.
 This code may only be used under the BSD style license found at http://polymer.github.io/LICENSE.txt
 The complete set of authors may be found at http://polymer.github.io/AUTHORS.txt
 The complete set of contributors may be found at http://polymer.github.io/CONTRIBUTORS.txt
 Code distributed by Google as part of the polymer project is also
 subject to an additional IP rights grant found at http://polymer.github.io/PATENTS.txt
*/
var Ta=Node.prototype.appendChild;function Ua(a){a=a.prototype;a.hasOwnProperty("append")||Object.defineProperty(a,"append",{configurable:!0,enumerable:!0,writable:!0,value:function(){for(var b=p(r.apply(0,arguments)),c=b.next();!c.done;c=b.next())c=c.value,Ta.call(this,typeof c==="string"?document.createTextNode(c):c)}})}Ua(Document);Ua(DocumentFragment);Ua(Element);var Va=Node.prototype.insertBefore,Wa,Xa,Ya=(Xa=(Wa=Object.getOwnPropertyDescriptor(Node.prototype,"firstChild"))==null?void 0:Wa.get)!=null?Xa:function(){return this.firstChild};function Za(a){a=a.prototype;a.hasOwnProperty("prepend")||Object.defineProperty(a,"prepend",{configurable:!0,enumerable:!0,writable:!0,value:function(){var b=r.apply(0,arguments),c=Ya.call(this);b=p(b);for(var d=b.next();!d.done;d=b.next())d=d.value,Va.call(this,typeof d==="string"?document.createTextNode(d):d,c)}})}Za(Document);
Za(DocumentFragment);Za(Element);var $a=Node.prototype.appendChild,ab=Node.prototype.removeChild,bb,cb,db=(cb=(bb=Object.getOwnPropertyDescriptor(Node.prototype,"firstChild"))==null?void 0:bb.get)!=null?cb:function(){return this.firstChild};
function eb(a){a=a.prototype;a.hasOwnProperty("replaceChildren")||Object.defineProperty(a,"replaceChildren",{configurable:!0,enumerable:!0,writable:!0,value:function(){for(var b=r.apply(0,arguments),c;(c=db.call(this))!==null;)ab.call(this,c);b=p(b);for(c=b.next();!c.done;c=b.next())c=c.value,$a.call(this,typeof c==="string"?document.createTextNode(c):c)}})}eb(Document);eb(DocumentFragment);eb(Element);var fb=Node.prototype.insertBefore,gb,hb,ib=(hb=(gb=Object.getOwnPropertyDescriptor(Node.prototype,"parentNode"))==null?void 0:gb.get)!=null?hb:function(){return this.parentNode},jb,kb,lb=(kb=(jb=Object.getOwnPropertyDescriptor(Node.prototype,"nextSibling"))==null?void 0:jb.get)!=null?kb:function(){return this.nextSibling};
function mb(a){a=a.prototype;a.hasOwnProperty("after")||Object.defineProperty(a,"after",{configurable:!0,enumerable:!0,writable:!0,value:function(){var b=r.apply(0,arguments),c=ib.call(this);if(c!==null){var d=lb.call(this);b=p(b);for(var e=b.next();!e.done;e=b.next())e=e.value,fb.call(c,typeof e==="string"?document.createTextNode(e):e,d)}}})}mb(CharacterData);mb(Element);var nb=Node.prototype.insertBefore,ob,pb,qb=(pb=(ob=Object.getOwnPropertyDescriptor(Node.prototype,"parentNode"))==null?void 0:ob.get)!=null?pb:function(){return this.parentNode};
function rb(a){a=a.prototype;a.hasOwnProperty("before")||Object.defineProperty(a,"before",{configurable:!0,enumerable:!0,writable:!0,value:function(){var b=r.apply(0,arguments),c=qb.call(this);if(c!==null){b=p(b);for(var d=b.next();!d.done;d=b.next())d=d.value,nb.call(c,typeof d==="string"?document.createTextNode(d):d,this)}}})}rb(CharacterData);rb(Element);var sb=Node.prototype.removeChild,tb,ub,vb=(ub=(tb=Object.getOwnPropertyDescriptor(Node.prototype,"parentNode"))==null?void 0:tb.get)!=null?ub:function(){return this.parentNode};function wb(a){a=a.prototype;a.hasOwnProperty("remove")||Object.defineProperty(a,"remove",{configurable:!0,enumerable:!0,writable:!0,value:function(){var b=vb.call(this);b&&sb.call(b,this)}})}wb(CharacterData);wb(Element);var xb=Node.prototype.insertBefore,yb=Node.prototype.removeChild,zb,Ab,Bb=(Ab=(zb=Object.getOwnPropertyDescriptor(Node.prototype,"parentNode"))==null?void 0:zb.get)!=null?Ab:function(){return this.parentNode};
function Cb(a){a=a.prototype;a.hasOwnProperty("replaceWith")||Object.defineProperty(a,"replaceWith",{configurable:!0,enumerable:!0,writable:!0,value:function(){var b=r.apply(0,arguments),c=Bb.call(this);if(c!==null){b=p(b);for(var d=b.next();!d.done;d=b.next())d=d.value,xb.call(c,typeof d==="string"?document.createTextNode(d):d,this);yb.call(c,this)}}})}Cb(CharacterData);Cb(Element);var Db=window.Element.prototype,Eb=window.HTMLElement.prototype,Fb=window.SVGElement.prototype;!Eb.hasOwnProperty("classList")||Db.hasOwnProperty("classList")||Fb.hasOwnProperty("classList")||Object.defineProperty(Db,"classList",Object.getOwnPropertyDescriptor(Eb,"classList"));/*

 Copyright (c) 2014 The Polymer Project Authors. All rights reserved.
 This code may only be used under the BSD style license found at
 http://polymer.github.io/LICENSE.txt The complete set of authors may be found
 at http://polymer.github.io/AUTHORS.txt The complete set of contributors may
 be found at http://polymer.github.io/CONTRIBUTORS.txt Code distributed by
 Google as part of the polymer project is also subject to an additional IP
 rights grant found at http://polymer.github.io/PATENTS.txt
*/
var v=window;v.WebComponents=v.WebComponents||{flags:{}};var Gb=document.querySelector('script[src*="webcomponents-lite.js"]'),Hb=/wc-(.+)/,w={};if(!w.noOpts){location.search.slice(1).split("&").forEach(function(a){a=a.split("=");var b;a[0]&&(b=a[0].match(Hb))&&(w[b[1]]=a[1]||!0)});if(Gb)for(var Ib=0,Jb=void 0;Jb=Gb.attributes[Ib];Ib++)Jb.name!=="src"&&(w[Jb.name]=Jb.value||!0);var Kb={};w.log&&w.log.split&&w.log.split(",").forEach(function(a){Kb[a]=!0});w.log=Kb}v.WebComponents.flags=w;var Lb=w.shadydom;
Lb&&(v.ShadyDOM=v.ShadyDOM||{},v.ShadyDOM.force=Lb);var Mb=w.register||w.ce;Mb&&window.customElements&&(v.customElements.forcePolyfill=Mb);var Nb=document.createElement("style");Nb.textContent="body {transition: opacity ease-in 0.2s; } \nbody[unresolved] {opacity: 0; display: block; overflow: hidden; position: relative; } \n";var Ob=document.querySelector("head");Ob.insertBefore(Nb,Ob.firstChild);var Pb=window.Document.prototype.createElement,Qb=window.Document.prototype.createElementNS,Rb=window.Document.prototype.importNode,Sb=window.Document.prototype.prepend,Tb=window.Document.prototype.append,Ub=window.DocumentFragment.prototype.prepend,Vb=window.DocumentFragment.prototype.append,Wb=window.Node.prototype.cloneNode,Xb=window.Node.prototype.appendChild,Yb=window.Node.prototype.insertBefore,Zb=window.Node.prototype.removeChild,$b=window.Node.prototype.replaceChild,ac=Object.getOwnPropertyDescriptor(window.Node.prototype,
"textContent"),bc=window.Element.prototype.attachShadow,cc=Object.getOwnPropertyDescriptor(window.Element.prototype,"innerHTML"),dc=window.Element.prototype.getAttribute,ec=window.Element.prototype.setAttribute,fc=window.Element.prototype.removeAttribute,hc=window.Element.prototype.toggleAttribute,ic=window.Element.prototype.getAttributeNS,jc=window.Element.prototype.setAttributeNS,kc=window.Element.prototype.removeAttributeNS,lc=window.Element.prototype.insertAdjacentElement,mc=window.Element.prototype.insertAdjacentHTML,
nc=window.Element.prototype.prepend,oc=window.Element.prototype.append,pc=window.Element.prototype.before,qc=window.Element.prototype.after,rc=window.Element.prototype.replaceWith,sc=window.Element.prototype.remove,tc=window.HTMLElement,uc=Object.getOwnPropertyDescriptor(window.HTMLElement.prototype,"innerHTML"),vc=window.HTMLElement.prototype.insertAdjacentElement,wc=window.HTMLElement.prototype.insertAdjacentHTML;var xc=function(){var a=new Set;"annotation-xml color-profile font-face font-face-src font-face-uri font-face-format font-face-name missing-glyph".split(" ").forEach(function(b){return a.add(b)});return a}();function yc(a){var b=xc.has(a);a=/^[a-z][.0-9_a-z]*-[-.0-9_a-z]*$/.test(a);return!b&&a}var zc=document.contains?document.contains.bind(document):document.documentElement.contains.bind(document.documentElement);
function x(a){var b=a.isConnected;if(b!==void 0)return b;if(zc(a))return!0;for(;a&&!(a.__CE_isImportDocument||a instanceof Document);)a=a.parentNode||(window.ShadowRoot&&a instanceof ShadowRoot?a.host:void 0);return!(!a||!(a.__CE_isImportDocument||a instanceof Document))}function Ac(a){var b=a.children;if(b)return Array.prototype.slice.call(b);b=[];for(a=a.firstChild;a;a=a.nextSibling)a.nodeType===Node.ELEMENT_NODE&&b.push(a);return b}
function Bc(a,b){for(;b&&b!==a&&!b.nextSibling;)b=b.parentNode;return b&&b!==a?b.nextSibling:null}
function Cc(a,b,c){for(var d=a;d;){if(d.nodeType===Node.ELEMENT_NODE){var e=d;b(e);var f=e.localName;if(f==="link"&&e.getAttribute("rel")==="import"){d=e.import;c===void 0&&(c=new Set);if(d instanceof Node&&!c.has(d))for(c.add(d),d=d.firstChild;d;d=d.nextSibling)Cc(d,b,c);d=Bc(a,e);continue}else if(f==="template"){d=Bc(a,e);continue}if(e=e.__CE_shadowRoot)for(e=e.firstChild;e;e=e.nextSibling)Cc(e,b,c)}d=d.firstChild?d.firstChild:Bc(a,d)}};function Dc(){var a=!(y==null||!y.noDocumentConstructionObserver),b=!(y==null||!y.shadyDomFastWalk);this.W=[];this.ya=[];this.N=!1;this.shadyDomFastWalk=b;this.Ic=!a}function Ec(a,b,c,d){var e=window.ShadyDOM;if(a.shadyDomFastWalk&&e&&e.inUse){if(b.nodeType===Node.ELEMENT_NODE&&c(b),b.querySelectorAll)for(a=e.nativeMethods.querySelectorAll.call(b,"*"),b=0;b<a.length;b++)c(a[b])}else Cc(b,c,d)}function Fc(a,b){a.N=!0;a.W.push(b)}function Gc(a,b){a.N=!0;a.ya.push(b)}
function Hc(a,b){a.N&&Ec(a,b,function(c){return Ic(a,c)})}function Ic(a,b){if(a.N&&!b.__CE_patched){b.__CE_patched=!0;for(var c=0;c<a.W.length;c++)a.W[c](b);for(c=0;c<a.ya.length;c++)a.ya[c](b)}}function z(a,b){var c=[];Ec(a,b,function(e){return c.push(e)});for(b=0;b<c.length;b++){var d=c[b];d.__CE_state===1?a.connectedCallback(d):Jc(a,d)}}function A(a,b){var c=[];Ec(a,b,function(e){return c.push(e)});for(b=0;b<c.length;b++){var d=c[b];d.__CE_state===1&&a.disconnectedCallback(d)}}
function B(a,b,c){c=c===void 0?{}:c;var d=c.Jc,e=c.upgrade||function(g){return Jc(a,g)},f=[];Ec(a,b,function(g){a.N&&Ic(a,g);if(g.localName==="link"&&g.getAttribute("rel")==="import"){var h=g.import;h instanceof Node&&(h.__CE_isImportDocument=!0,h.__CE_registry=document.__CE_registry);h&&h.readyState==="complete"?h.__CE_documentLoadHandled=!0:g.addEventListener("load",function(){var k=g.import;if(!k.__CE_documentLoadHandled){k.__CE_documentLoadHandled=!0;var l=new Set;d&&(d.forEach(function(n){return l.add(n)}),
l.delete(k));B(a,k,{Jc:l,upgrade:e})}})}else f.push(g)},d);for(b=0;b<f.length;b++)e(f[b])}function Jc(a,b){try{var c=a.Ob(b.ownerDocument,b.localName);c&&a.lc(b,c)}catch(d){Kc(d)}}m=Dc.prototype;
m.lc=function(a,b){if(a.__CE_state===void 0){b.constructionStack.push(a);try{try{if(new b.constructorFunction!==a)throw Error("The custom element constructor did not produce the element being upgraded.");}finally{b.constructionStack.pop()}}catch(f){throw a.__CE_state=2,f;}a.__CE_state=1;a.__CE_definition=b;if(b.attributeChangedCallback&&a.hasAttributes()){b=b.observedAttributes;for(var c=0;c<b.length;c++){var d=b[c],e=a.getAttribute(d);e!==null&&this.attributeChangedCallback(a,d,null,e,null)}}x(a)&&
this.connectedCallback(a)}};m.connectedCallback=function(a){var b=a.__CE_definition;if(b.connectedCallback)try{b.connectedCallback.call(a)}catch(c){Kc(c)}};m.disconnectedCallback=function(a){var b=a.__CE_definition;if(b.disconnectedCallback)try{b.disconnectedCallback.call(a)}catch(c){Kc(c)}};m.attributeChangedCallback=function(a,b,c,d,e){var f=a.__CE_definition;if(f.attributeChangedCallback&&f.observedAttributes.indexOf(b)>-1)try{f.attributeChangedCallback.call(a,b,c,d,e)}catch(g){Kc(g)}};
m.Ob=function(a,b){var c=a.__CE_registry;if(c&&(a.defaultView||a.__CE_isImportDocument))return Lc(c,b)};
function Mc(a,b,c,d){var e=b.__CE_registry;if(e&&(d===null||d==="http://www.w3.org/1999/xhtml")&&(e=Lc(e,c)))try{var f=new e.constructorFunction;if(f.__CE_state===void 0||f.__CE_definition===void 0)throw Error("Failed to construct '"+c+"': The returned value was not constructed with the HTMLElement constructor.");if(f.namespaceURI!=="http://www.w3.org/1999/xhtml")throw Error("Failed to construct '"+c+"': The constructed element's namespace must be the HTML namespace.");if(f.hasAttributes())throw Error("Failed to construct '"+
c+"': The constructed element must not have any attributes.");if(f.firstChild!==null)throw Error("Failed to construct '"+c+"': The constructed element must not have any children.");if(f.parentNode!==null)throw Error("Failed to construct '"+c+"': The constructed element must not have a parent node.");if(f.ownerDocument!==b)throw Error("Failed to construct '"+c+"': The constructed element's owner document is incorrect.");if(f.localName!==c)throw Error("Failed to construct '"+c+"': The constructed element's local name is incorrect.");
return f}catch(g){return Kc(g),b=d===null?Pb.call(b,c):Qb.call(b,d,c),Object.setPrototypeOf(b,HTMLUnknownElement.prototype),b.__CE_state=2,b.__CE_definition=void 0,Ic(a,b),b}b=d===null?Pb.call(b,c):Qb.call(b,d,c);Ic(a,b);return b}
function Kc(a){var b="",c="",d=0,e=0;a instanceof Error?(b=a.message,c=a.sourceURL||a.fileName||"",d=a.line||a.lineNumber||0,e=a.column||a.columnNumber||0):b="Uncaught "+String(a);var f=void 0;ErrorEvent.prototype.initErrorEvent===void 0?f=new ErrorEvent("error",{cancelable:!0,message:b,filename:c,lineno:d,colno:e,error:a}):(f=document.createEvent("ErrorEvent"),f.initErrorEvent("error",!1,!0,b,c,d),f.preventDefault=function(){Object.defineProperty(this,"defaultPrevented",{configurable:!0,get:function(){return!0}})});
f.error===void 0&&Object.defineProperty(f,"error",{configurable:!0,enumerable:!0,get:function(){return a}});window.dispatchEvent(f);f.defaultPrevented||console.error(a)};function Nc(){var a=this;this.u=void 0;this.Ya=new Promise(function(b){a.Yb=b})}Nc.prototype.resolve=function(a){if(this.u)throw Error("Already resolved.");this.u=a;this.Yb(a)};function Oc(a){var b=document;this.O=void 0;this.H=a;this.fa=b;B(this.H,this.fa);this.fa.readyState==="loading"&&(this.O=new MutationObserver(this.Kb.bind(this)),this.O.observe(this.fa,{childList:!0,subtree:!0}))}Oc.prototype.disconnect=function(){this.O&&this.O.disconnect()};Oc.prototype.Kb=function(a){var b=this.fa.readyState;b!=="interactive"&&b!=="complete"||this.disconnect();for(b=0;b<a.length;b++)for(var c=a[b].addedNodes,d=0;d<c.length;d++)B(this.H,c[d])};function C(a){this.ha=new Map;this.ia=new Map;this.La=new Map;this.va=!1;this.Da=new Map;this.ga=function(b){return b()};this.M=!1;this.ma=[];this.H=a;this.Na=a.Ic?new Oc(a):void 0}m=C.prototype;m.zc=function(a,b){var c=this;if(!(b instanceof Function))throw new TypeError("Custom element constructor getters must be functions.");Pc(this,a);this.ha.set(a,b);this.ma.push(a);this.M||(this.M=!0,this.ga(function(){return c.Pa()}))};
m.define=function(a,b){var c=this;if(!(b instanceof Function))throw new TypeError("Custom element constructors must be functions.");Pc(this,a);Qc(this,a,b);this.ma.push(a);this.M||(this.M=!0,this.ga(function(){return c.Pa()}))};
function Pc(a,b){if(!yc(b))throw new SyntaxError("The element name '"+b+"' is not valid.");if(Lc(a,b)&&!window.enableHotReplacement)throw Error("A custom element with name '"+(b+"' has already been defined."));if(a.va)throw Error("A custom element is already being defined.");}
function Qc(a,b,c){a.va=!0;var d;try{var e=c.prototype;if(!(e instanceof Object))throw new TypeError("The custom element constructor's prototype is not an object.");var f=function(n){var t=e[n];if(t!==void 0&&!(t instanceof Function))throw Error("The '"+n+"' callback must be a function.");return t};var g=f("connectedCallback");var h=f("disconnectedCallback");var k=f("adoptedCallback");var l=(d=f("attributeChangedCallback"))&&c.observedAttributes||[]}catch(n){throw n;}finally{a.va=!1}c={localName:b,
constructorFunction:c,connectedCallback:g,disconnectedCallback:h,adoptedCallback:k,attributeChangedCallback:d,observedAttributes:l,constructionStack:[]};a.ia.set(b,c);a.La.set(c.constructorFunction,c);return c}m.upgrade=function(a){B(this.H,a)};
m.Pa=function(){var a=this;if(this.M!==!1){this.M=!1;for(var b=[],c=this.ma,d=new Map,e=0;e<c.length;e++)d.set(c[e],[]);B(this.H,document,{upgrade:function(k){if(k.__CE_state===void 0){var l=k.localName,n=d.get(l);n?n.push(k):a.ia.has(l)&&b.push(k)}}});for(e=0;e<b.length;e++)Jc(this.H,b[e]);for(e=0;e<c.length;e++){for(var f=c[e],g=d.get(f),h=0;h<g.length;h++)Jc(this.H,g[h]);(f=this.Da.get(f))&&f.resolve(void 0)}c.length=0}};m.get=function(a){if(a=Lc(this,a))return a.constructorFunction};
m.whenDefined=function(a){if(!yc(a))return Promise.reject(new SyntaxError("'"+a+"' is not a valid custom element name."));var b=this.Da.get(a);if(b)return b.Ya;b=new Nc;this.Da.set(a,b);var c=this.ia.has(a)||this.ha.has(a);a=this.ma.indexOf(a)===-1;c&&a&&b.resolve(void 0);return b.Ya};m.polyfillWrapFlushCallback=function(a){this.Na&&this.Na.disconnect();var b=this.ga;this.ga=function(c){return a(function(){return b(c)})}};
function Lc(a,b){var c=a.ia.get(b);if(c)return c;if(c=a.ha.get(b)){a.ha.delete(b);try{return Qc(a,b,c())}catch(d){Kc(d)}}}C.prototype.define=C.prototype.define;C.prototype.upgrade=C.prototype.upgrade;C.prototype.get=C.prototype.get;C.prototype.whenDefined=C.prototype.whenDefined;C.prototype.polyfillDefineLazy=C.prototype.zc;C.prototype.polyfillWrapFlushCallback=C.prototype.polyfillWrapFlushCallback;function Rc(a,b,c){function d(e){return function(){for(var f=r.apply(0,arguments),g=[],h=[],k=0;k<f.length;k++){var l=f[k];l instanceof Element&&x(l)&&h.push(l);if(l instanceof DocumentFragment)for(l=l.firstChild;l;l=l.nextSibling)g.push(l);else g.push(l)}e.apply(this,f);for(f=0;f<h.length;f++)A(a,h[f]);if(x(this))for(h=0;h<g.length;h++)f=g[h],f instanceof Element&&z(a,f)}}c.prepend!==void 0&&(b.prepend=d(c.prepend));c.append!==void 0&&(b.append=d(c.append))};function Sc(a){Document.prototype.createElement=function(b){return Mc(a,this,b,null)};Document.prototype.importNode=function(b,c){b=Rb.call(this,b,!!c);this.__CE_registry?B(a,b):Hc(a,b);return b};Document.prototype.createElementNS=function(b,c){return Mc(a,this,c,b)};Rc(a,Document.prototype,{prepend:Sb,append:Tb})};function Tc(a){function b(d){return function(){for(var e=r.apply(0,arguments),f=[],g=[],h=0;h<e.length;h++){var k=e[h];k instanceof Element&&x(k)&&g.push(k);if(k instanceof DocumentFragment)for(k=k.firstChild;k;k=k.nextSibling)f.push(k);else f.push(k)}d.apply(this,e);for(e=0;e<g.length;e++)A(a,g[e]);if(x(this))for(g=0;g<f.length;g++)e=f[g],e instanceof Element&&z(a,e)}}var c=Element.prototype;pc!==void 0&&(c.before=b(pc));qc!==void 0&&(c.after=b(qc));rc!==void 0&&(c.replaceWith=function(){for(var d=
r.apply(0,arguments),e=[],f=[],g=0;g<d.length;g++){var h=d[g];h instanceof Element&&x(h)&&f.push(h);if(h instanceof DocumentFragment)for(h=h.firstChild;h;h=h.nextSibling)e.push(h);else e.push(h)}g=x(this);rc.apply(this,d);for(d=0;d<f.length;d++)A(a,f[d]);if(g)for(A(a,this),f=0;f<e.length;f++)d=e[f],d instanceof Element&&z(a,d)});sc!==void 0&&(c.remove=function(){var d=x(this);sc.call(this);d&&A(a,this)})};function Uc(a){function b(e,f){Object.defineProperty(e,"innerHTML",{enumerable:f.enumerable,configurable:!0,get:f.get,set:function(g){var h=this,k=void 0;x(this)&&(k=[],Ec(a,this,function(t){t!==h&&k.push(t)}));f.set.call(this,g);if(k)for(var l=0;l<k.length;l++){var n=k[l];n.__CE_state===1&&a.disconnectedCallback(n)}this.ownerDocument.__CE_registry?B(a,this):Hc(a,this);return g}})}function c(e,f){e.insertAdjacentElement=function(g,h){var k=x(h);g=f.call(this,g,h);k&&A(a,h);x(g)&&z(a,h);return g}}
function d(e,f){function g(h,k){for(var l=[];h!==k;h=h.nextSibling)l.push(h);for(k=0;k<l.length;k++)B(a,l[k])}e.insertAdjacentHTML=function(h,k){h=h.toLowerCase();if(h==="beforebegin"){var l=this.previousSibling;f.call(this,h,k);g(l||this.parentNode.firstChild,this)}else if(h==="afterbegin")l=this.firstChild,f.call(this,h,k),g(this.firstChild,l);else if(h==="beforeend")l=this.lastChild,f.call(this,h,k),g(l||this.firstChild,null);else if(h==="afterend")l=this.nextSibling,f.call(this,h,k),g(this.nextSibling,
l);else throw new SyntaxError("The value provided ("+String(h)+") is not one of 'beforebegin', 'afterbegin', 'beforeend', or 'afterend'.");}}bc&&(Element.prototype.attachShadow=function(e){e=bc.call(this,e);if(a.N&&!e.__CE_patched){e.__CE_patched=!0;for(var f=0;f<a.W.length;f++)a.W[f](e)}return this.__CE_shadowRoot=e});cc&&cc.get?b(Element.prototype,cc):uc&&uc.get?b(HTMLElement.prototype,uc):Gc(a,function(e){b(e,{enumerable:!0,configurable:!0,get:function(){return Wb.call(this,!0).innerHTML},set:function(f){var g=
this.localName==="template",h=g?this.content:this,k=Qb.call(document,this.namespaceURI,this.localName);for(k.innerHTML=f;h.childNodes.length>0;)Zb.call(h,h.childNodes[0]);for(f=g?k.content:k;f.childNodes.length>0;)Xb.call(h,f.childNodes[0])}})});Element.prototype.setAttribute=function(e,f){if(this.__CE_state!==1)return ec.call(this,e,f);var g=dc.call(this,e);ec.call(this,e,f);f=dc.call(this,e);a.attributeChangedCallback(this,e,g,f,null)};Element.prototype.setAttributeNS=function(e,f,g){if(this.__CE_state!==
1)return jc.call(this,e,f,g);var h=ic.call(this,e,f);jc.call(this,e,f,g);g=ic.call(this,e,f);a.attributeChangedCallback(this,f,h,g,e)};Element.prototype.removeAttribute=function(e){if(this.__CE_state!==1)return fc.call(this,e);var f=dc.call(this,e);fc.call(this,e);f!==null&&a.attributeChangedCallback(this,e,f,null,null)};hc&&(Element.prototype.toggleAttribute=function(e,f){if(this.__CE_state!==1)return hc.call(this,e,f);var g=dc.call(this,e),h=g!==null;f=hc.call(this,e,f);if(h!==f){var k;a==null||
(k=a.attributeChangedCallback)==null||k.call(a,this,e,g,f?"":null,null)}return f});Element.prototype.removeAttributeNS=function(e,f){if(this.__CE_state!==1)return kc.call(this,e,f);var g=ic.call(this,e,f);kc.call(this,e,f);var h=ic.call(this,e,f);g!==h&&a.attributeChangedCallback(this,f,g,h,e)};vc?c(HTMLElement.prototype,vc):lc&&c(Element.prototype,lc);wc?d(HTMLElement.prototype,wc):mc&&d(Element.prototype,mc);Rc(a,Element.prototype,{prepend:nc,append:oc});Tc(a)};var Vc={};function Wc(a){function b(){var c=this.constructor;var d=document.__CE_registry.La.get(c);if(!d)throw Error("Failed to construct a custom element: The constructor was not registered with `customElements`.");var e=d.constructionStack;if(e.length===0)return e=Pb.call(document,d.localName),Object.setPrototypeOf(e,c.prototype),e.__CE_state=1,e.__CE_definition=d,Ic(a,e),e;var f=e.length-1,g=e[f];if(g===Vc)throw Error("Failed to construct '"+d.localName+"': This element was already constructed.");e[f]=
Vc;Object.setPrototypeOf(g,c.prototype);Ic(a,g);return g}b.prototype=tc.prototype;Object.defineProperty(HTMLElement.prototype,"constructor",{writable:!0,configurable:!0,enumerable:!1,value:b});window.HTMLElement=b};function Xc(a){function b(c,d){Object.defineProperty(c,"textContent",{enumerable:d.enumerable,configurable:!0,get:d.get,set:function(e){if(this.nodeType===Node.TEXT_NODE)d.set.call(this,e);else{var f=void 0;if(this.firstChild){var g=this.childNodes,h=g.length;if(h>0&&x(this)){f=Array(h);for(var k=0;k<h;k++)f[k]=g[k]}}d.set.call(this,e);if(f)for(e=0;e<f.length;e++)A(a,f[e])}}})}Node.prototype.insertBefore=function(c,d){if(c instanceof DocumentFragment){var e=Ac(c);c=Yb.call(this,c,d);if(x(this))for(d=
0;d<e.length;d++)z(a,e[d]);return c}e=c instanceof Element&&x(c);d=Yb.call(this,c,d);e&&A(a,c);x(this)&&z(a,c);return d};Node.prototype.appendChild=function(c){if(c instanceof DocumentFragment){var d=Ac(c);c=Xb.call(this,c);if(x(this))for(var e=0;e<d.length;e++)z(a,d[e]);return c}d=c instanceof Element&&x(c);e=Xb.call(this,c);d&&A(a,c);x(this)&&z(a,c);return e};Node.prototype.cloneNode=function(c){c=Wb.call(this,!!c);this.ownerDocument.__CE_registry?B(a,c):Hc(a,c);return c};Node.prototype.removeChild=
function(c){var d=c instanceof Element&&x(c),e=Zb.call(this,c);d&&A(a,c);return e};Node.prototype.replaceChild=function(c,d){if(c instanceof DocumentFragment){var e=Ac(c);c=$b.call(this,c,d);if(x(this))for(A(a,d),d=0;d<e.length;d++)z(a,e[d]);return c}e=c instanceof Element&&x(c);var f=$b.call(this,c,d),g=x(this);g&&A(a,d);e&&A(a,c);g&&z(a,c);return f};ac&&ac.get?b(Node.prototype,ac):Fc(a,function(c){b(c,{enumerable:!0,configurable:!0,get:function(){for(var d=[],e=this.firstChild;e;e=e.nextSibling)e.nodeType!==
Node.COMMENT_NODE&&d.push(e.textContent);return d.join("")},set:function(d){for(;this.firstChild;)Zb.call(this,this.firstChild);d!=null&&d!==""&&Xb.call(this,document.createTextNode(d))}})})};var y=window.customElements;function Yc(){var a=new Dc;Wc(a);Sc(a);Rc(a,DocumentFragment.prototype,{prepend:Ub,append:Vb});Xc(a);Uc(a);window.CustomElementRegistry=C;a=new C(a);document.__CE_registry=a;Object.defineProperty(window,"customElements",{configurable:!0,enumerable:!0,value:a})}y&&!y.forcePolyfill&&typeof y.define=="function"&&typeof y.get=="function"||Yc();window.__CE_installPolyfill=Yc;/*

Copyright (c) 2016 The Polymer Project Authors. All rights reserved.
This code may only be used under the BSD style license found at http://polymer.github.io/LICENSE.txt
The complete set of authors may be found at http://polymer.github.io/AUTHORS.txt
The complete set of contributors may be found at http://polymer.github.io/CONTRIBUTORS.txt
Code distributed by Google as part of the polymer project is also
subject to an additional IP rights grant found at http://polymer.github.io/PATENTS.txt
*/
function Zc(){}Zc.prototype.toJSON=function(){return{}};function E(a){a.__shady||(a.__shady=new Zc);return a.__shady}function F(a){return a&&a.__shady};var G=window.ShadyDOM||{};G.sc=!(!Element.prototype.attachShadow||!Node.prototype.getRootNode);var $c=Object.getOwnPropertyDescriptor(Node.prototype,"firstChild");G.j=!!($c&&$c.configurable&&$c.get);G.inUse=G.force||!G.sc;G.noPatch=G.noPatch||!1;G.aa=G.preferPerformance;G.Ga=G.noPatch==="on-demand";var ad;var bd=G.querySelectorImplementation;ad=["native","selectorEngine"].indexOf(bd)>-1?bd:void 0;G.querySelectorImplementation=ad;var cd=navigator.userAgent.match("Trident");G.mb=cd;
function dd(){return Document.prototype.msElementsFromPoint?"msElementsFromPoint":"elementsFromPoint"}function H(a){return(a=F(a))&&a.firstChild!==void 0}function I(a){return a instanceof ShadowRoot}function ed(a){return(a=(a=F(a))&&a.root)&&a.Ra()}var fd=Element.prototype,gd=fd.matches||fd.matchesSelector||fd.mozMatchesSelector||fd.msMatchesSelector||fd.oMatchesSelector||fd.webkitMatchesSelector,hd=document.createTextNode(""),id=0,jd=[];
(new MutationObserver(function(){for(;jd.length;)try{jd.shift()()}catch(a){throw hd.textContent=id++,a;}})).observe(hd,{characterData:!0});function kd(a){jd.push(a);hd.textContent=id++}var ld=document.contains?function(a,b){return a.__shady_native_contains(b)}:function(a,b){return a===b||a.documentElement&&a.documentElement.__shady_native_contains(b)};function md(a,b){for(;b;){if(b==a)return!0;b=b.__shady_parentNode}return!1}
function nd(a){for(var b=a.length-1;b>=0;b--){var c=a[b],d=c.getAttribute("id")||c.getAttribute("name");d&&d!=="length"&&isNaN(d)&&(a[d]=c)}a.item=function(e){return a[e]};a.namedItem=function(e){if(e!=="length"&&isNaN(e)&&a[e])return a[e];for(var f=p(a),g=f.next();!g.done;g=f.next())if(g=g.value,(g.getAttribute("id")||g.getAttribute("name"))==e)return g;return null};return a}function od(a){var b=[];for(a=a.__shady_native_firstChild;a;a=a.__shady_native_nextSibling)b.push(a);return b}
function pd(a){var b=[];for(a=a.__shady_firstChild;a;a=a.__shady_nextSibling)b.push(a);return b}function qd(a,b,c){c.configurable=!0;if(c.value)a[b]=c.value;else try{Object.defineProperty(a,b,c)}catch(d){}}function J(a,b,c,d){c=c===void 0?"":c;for(var e in b)d&&d.indexOf(e)>=0||qd(a,c+e,b[e])}function rd(a,b){for(var c in b)c in a&&qd(a,c,b[c])}function K(a){var b={};Object.getOwnPropertyNames(a).forEach(function(c){b[c]=Object.getOwnPropertyDescriptor(a,c)});return b}
function sd(a,b){for(var c=Object.getOwnPropertyNames(b),d=0,e;d<c.length;d++)e=c[d],a[e]=b[e]}function td(a){return a instanceof Node?a:document.createTextNode(""+a)}function ud(){var a=r.apply(0,arguments);if(a.length===1)return td(a[0]);var b=document.createDocumentFragment();a=p(a);for(var c=a.next();!c.done;c=a.next())b.appendChild(td(c.value));return b}
function vd(a){var b;for(b=b===void 0?1:b;b>0;b--)a=a.reduce(function(c,d){Array.isArray(d)?c.push.apply(c,q(d)):c.push(d);return c},[]);return a}function wd(a){var b=[],c=new Set;a=p(a);for(var d=a.next();!d.done;d=a.next())d=d.value,c.has(d)||(b.push(d),c.add(d));return b};var xd=[],yd;function zd(a){yd||(yd=!0,kd(Ad));xd.push(a)}function Ad(){yd=!1;for(var a=!!xd.length;xd.length;)xd.shift()();return a}Ad.list=xd;function Bd(){this.ka=!1;this.addedNodes=[];this.removedNodes=[];this.pa=new Set}function Cd(a){a.ka||(a.ka=!0,kd(function(){a.flush()}))}Bd.prototype.flush=function(){if(this.ka){this.ka=!1;var a=this.takeRecords();a.length&&this.pa.forEach(function(b){b(a)})}};Bd.prototype.takeRecords=function(){if(this.addedNodes.length||this.removedNodes.length){var a=[{addedNodes:this.addedNodes,removedNodes:this.removedNodes}];this.addedNodes=[];this.removedNodes=[];return a}return[]};
function Dd(a,b){var c=E(a);c.Z||(c.Z=new Bd);c.Z.pa.add(b);var d=c.Z;return{tb:b,O:d,Rb:a,takeRecords:function(){return d.takeRecords()}}}function Ed(a){var b=a&&a.O;b&&(b.pa.delete(a.tb),b.pa.size||(E(a.Rb).Z=null))}
function Fd(a,b){var c=b.getRootNode();return a.map(function(d){var e=c===d.target.getRootNode();if(e&&d.addedNodes){if(e=[].slice.call(d.addedNodes).filter(function(f){return c===f.getRootNode()}),e.length)return d=Object.create(d),Object.defineProperty(d,"addedNodes",{value:e,configurable:!0}),d}else if(e)return d}).filter(function(d){return d})};var Gd=/[&\u00A0"]/g,Hd=/[&\u00A0<>]/g;function Id(a){switch(a){case "&":return"&amp;";case "<":return"&lt;";case ">":return"&gt;";case '"':return"&quot;";case "\u00a0":return"&nbsp;"}}function Jd(a){for(var b={},c=0;c<a.length;c++)b[a[c]]=!0;return b}var Kd=Jd("area base br col command embed hr img input keygen link meta param source track wbr".split(" ")),Ld=Jd("style script xmp iframe noembed noframes plaintext noscript".split(" "));
function Md(a,b){a.localName==="template"&&(a=a.content);for(var c="",d=b?b(a):a.childNodes,e=0,f=d.length,g=void 0;e<f&&(g=d[e]);e++){a:{var h=g;var k=a,l=b;switch(h.nodeType){case Node.ELEMENT_NODE:k=h.localName;for(var n="<"+k,t=h.attributes,D=0,wa;wa=t[D];D++)n+=" "+wa.name+'="'+wa.value.replace(Gd,Id)+'"';n+=">";h=Kd[k]?n:n+Md(h,l)+"</"+k+">";break a;case Node.TEXT_NODE:h=h.data;h=k&&Ld[k.localName]?h:h.replace(Hd,Id);break a;case Node.COMMENT_NODE:h="\x3c!--"+h.data+"--\x3e";break a;default:throw window.console.error(h),
Error("not implemented");}h=void 0}c+=h}return c};var Nd=G.j,Od={querySelector:function(a){return this.__shady_native_querySelector(a)},querySelectorAll:function(a){return this.__shady_native_querySelectorAll(a)}},Pd={};function Qd(a){Pd[a]=function(b){return b["__shady_native_"+a]}}function Rd(a,b){J(a,b,"__shady_native_");for(var c in b)Qd(c)}function L(a,b){b=b===void 0?[]:b;for(var c=0;c<b.length;c++){var d=b[c],e=Object.getOwnPropertyDescriptor(a,d);e&&(Object.defineProperty(a,"__shady_native_"+d,e),e.value?Od[d]||(Od[d]=e.value):Qd(d))}}
var M=document.createTreeWalker(document,NodeFilter.SHOW_ALL,null,!1),N=document.createTreeWalker(document,NodeFilter.SHOW_ELEMENT,null,!1),Sd=document.implementation.createHTMLDocument("inert");function Td(a){for(var b;b=a.__shady_native_firstChild;)a.__shady_native_removeChild(b)}var Ud=["firstElementChild","lastElementChild","children","childElementCount"],Vd=["querySelector","querySelectorAll","append","prepend","replaceChildren"];
function Wd(){var a=["dispatchEvent","addEventListener","removeEventListener"];window.EventTarget?(L(window.EventTarget.prototype,a),window.__shady_native_addEventListener===void 0&&L(Window.prototype,a)):(L(Node.prototype,a),L(Window.prototype,a),L(XMLHttpRequest.prototype,a));Nd?L(Node.prototype,"parentNode firstChild lastChild previousSibling nextSibling childNodes parentElement textContent".split(" ")):Rd(Node.prototype,{parentNode:{get:function(){M.currentNode=this;return M.parentNode()}},firstChild:{get:function(){M.currentNode=
this;return M.firstChild()}},lastChild:{get:function(){M.currentNode=this;return M.lastChild()}},previousSibling:{get:function(){M.currentNode=this;return M.previousSibling()}},nextSibling:{get:function(){M.currentNode=this;return M.nextSibling()}},childNodes:{get:function(){var b=[];M.currentNode=this;for(var c=M.firstChild();c;)b.push(c),c=M.nextSibling();return b}},parentElement:{get:function(){N.currentNode=this;return N.parentNode()}},textContent:{get:function(){switch(this.nodeType){case Node.ELEMENT_NODE:case Node.DOCUMENT_FRAGMENT_NODE:for(var b=
document.createTreeWalker(this,NodeFilter.SHOW_TEXT,null,!1),c="",d;d=b.nextNode();)c+=d.nodeValue;return c;default:return this.nodeValue}},set:function(b){if(typeof b==="undefined"||b===null)b="";switch(this.nodeType){case Node.ELEMENT_NODE:case Node.DOCUMENT_FRAGMENT_NODE:Td(this);(b.length>0||this.nodeType===Node.ELEMENT_NODE)&&this.__shady_native_insertBefore(document.createTextNode(b),void 0);break;default:this.nodeValue=b}}}});L(Node.prototype,"appendChild insertBefore removeChild replaceChild cloneNode contains".split(" "));
L(HTMLElement.prototype,["parentElement","contains"]);a={firstElementChild:{get:function(){N.currentNode=this;return N.firstChild()}},lastElementChild:{get:function(){N.currentNode=this;return N.lastChild()}},children:{get:function(){var b=[];N.currentNode=this;for(var c=N.firstChild();c;)b.push(c),c=N.nextSibling();return nd(b)}},childElementCount:{get:function(){return this.children?this.children.length:0}}};Nd?(L(Element.prototype,Ud),L(Element.prototype,["previousElementSibling","nextElementSibling",
"innerHTML","className"]),L(HTMLElement.prototype,["children","innerHTML","className"])):(Rd(Element.prototype,a),Rd(Element.prototype,{previousElementSibling:{get:function(){N.currentNode=this;return N.previousSibling()}},nextElementSibling:{get:function(){N.currentNode=this;return N.nextSibling()}},innerHTML:{get:function(){return Md(this,od)},set:function(b){var c=this.localName==="template"?this.content:this;Td(c);var d=this.localName||"div";d=this.namespaceURI&&this.namespaceURI!==Sd.namespaceURI?
Sd.createElementNS(this.namespaceURI,d):Sd.createElement(d);d.innerHTML=b;for(b=this.localName==="template"?d.content:d;d=b.__shady_native_firstChild;)c.__shady_native_insertBefore(d,void 0)}},className:{get:function(){return this.getAttribute("class")||""},set:function(b){this.setAttribute("class",b)}}}));L(Element.prototype,"setAttribute getAttribute hasAttribute removeAttribute toggleAttribute focus blur".split(" "));L(Element.prototype,Vd);L(HTMLElement.prototype,["focus","blur"]);window.HTMLTemplateElement&&
L(window.HTMLTemplateElement.prototype,["innerHTML"]);Nd?L(DocumentFragment.prototype,Ud):Rd(DocumentFragment.prototype,a);L(DocumentFragment.prototype,Vd);Nd?(L(Document.prototype,Ud),L(Document.prototype,["activeElement"])):Rd(Document.prototype,a);L(Document.prototype,["importNode","getElementById","elementFromPoint",dd()]);L(Document.prototype,Vd)};var Xd=K({get childNodes(){return this.__shady_childNodes},get firstChild(){return this.__shady_firstChild},get lastChild(){return this.__shady_lastChild},get childElementCount(){return this.__shady_childElementCount},get children(){return this.__shady_children},get firstElementChild(){return this.__shady_firstElementChild},get lastElementChild(){return this.__shady_lastElementChild},get shadowRoot(){return this.__shady_shadowRoot}}),Yd=K({get textContent(){return this.__shady_textContent},set textContent(a){this.__shady_textContent=
a},get innerHTML(){return this.__shady_innerHTML},set innerHTML(a){this.__shady_innerHTML=a}}),Zd=K({get parentElement(){return this.__shady_parentElement},get parentNode(){return this.__shady_parentNode},get nextSibling(){return this.__shady_nextSibling},get previousSibling(){return this.__shady_previousSibling},get nextElementSibling(){return this.__shady_nextElementSibling},get previousElementSibling(){return this.__shady_previousElementSibling},get className(){return this.__shady_className},set className(a){this.__shady_className=
a}});function $d(a){for(var b in a){var c=a[b];c&&(c.enumerable=!1)}}$d(Xd);$d(Yd);$d(Zd);var ae=G.j||G.noPatch===!0,be=ae?function(){}:function(a){var b=E(a);b.pb||(b.pb=!0,rd(a,Zd))},ce=ae?function(){}:function(a){var b=E(a);b.ob||(b.ob=!0,rd(a,Xd),window.customElements&&window.customElements.polyfillWrapFlushCallback&&!G.noPatch||rd(a,Yd))};var de="__eventWrappers"+Date.now(),ee=function(){var a=Object.getOwnPropertyDescriptor(Event.prototype,"composed");return a?function(b){return a.get.call(b)}:null}(),fe=function(){function a(){}var b=!1,c={get capture(){b=!0;return!1}};window.addEventListener("test",a,c);window.removeEventListener("test",a,c);return b}();function ge(a){if(a===null||typeof a!=="object"&&typeof a!=="function"){var b=!!a;var c=!1}else{b=!!a.capture;c=!!a.once;var d=a.K}return{jb:d,capture:b,once:c,ib:fe?a:b}}
var he={blur:!0,focus:!0,focusin:!0,focusout:!0,click:!0,dblclick:!0,mousedown:!0,mouseenter:!0,mouseleave:!0,mousemove:!0,mouseout:!0,mouseover:!0,mouseup:!0,wheel:!0,beforeinput:!0,input:!0,keydown:!0,keyup:!0,compositionstart:!0,compositionupdate:!0,compositionend:!0,touchstart:!0,touchend:!0,touchmove:!0,touchcancel:!0,pointerover:!0,pointerenter:!0,pointerdown:!0,pointermove:!0,pointerup:!0,pointercancel:!0,pointerout:!0,pointerleave:!0,gotpointercapture:!0,lostpointercapture:!0,dragstart:!0,
drag:!0,dragenter:!0,dragleave:!0,dragover:!0,drop:!0,dragend:!0,DOMActivate:!0,DOMFocusIn:!0,DOMFocusOut:!0,keypress:!0},ie={DOMAttrModified:!0,DOMAttributeNameChanged:!0,DOMCharacterDataModified:!0,DOMElementNameChanged:!0,DOMNodeInserted:!0,DOMNodeInsertedIntoDocument:!0,DOMNodeRemoved:!0,DOMNodeRemovedFromDocument:!0,DOMSubtreeModified:!0};function je(a){return a instanceof Node?a.__shady_getRootNode():a}
function ke(a,b){var c=[],d=a;for(a=je(a);d;)c.push(d),d=d.__shady_assignedSlot?d.__shady_assignedSlot:d.nodeType===Node.DOCUMENT_FRAGMENT_NODE&&d.host&&(b||d!==a)?d.host:d.__shady_parentNode;c[c.length-1]===document&&c.push(window);return c}function le(a){a.__composedPath||(a.__composedPath=ke(a.target,!0));return a.__composedPath}function me(a,b){if(!I)return a;a=ke(a,!0);for(var c=0,d,e=void 0,f,g=void 0;c<b.length;c++)if(d=b[c],f=je(d),f!==e&&(g=a.indexOf(f),e=f),!I(f)||g>-1)return d}
var ne={get composed(){this.__composed===void 0&&(ee?this.__composed=this.type==="focusin"||this.type==="focusout"||ee(this):this.isTrusted!==!1&&(this.__composed=he[this.type]));return this.__composed||!1},composedPath:function(){this.__composedPath||(this.__composedPath=ke(this.__target,this.composed));return this.__composedPath},get target(){return me(this.currentTarget||this.__previousCurrentTarget,this.composedPath())},get relatedTarget(){if(!this.__relatedTarget)return null;this.__relatedTargetComposedPath||
(this.__relatedTargetComposedPath=ke(this.__relatedTarget,!0));return me(this.currentTarget||this.__previousCurrentTarget,this.__relatedTargetComposedPath)},stopPropagation:function(){Event.prototype.stopPropagation.call(this);this.ta=!0},stopImmediatePropagation:function(){Event.prototype.stopImmediatePropagation.call(this);this.ta=this.__immediatePropagationStopped=!0}},oe=G.j&&Object.getOwnPropertyDescriptor(Event.prototype,"eventPhase");
oe&&(Object.defineProperty(ne,"eventPhase",{get:function(){return this.currentTarget===this.target?Event.AT_TARGET:this.__shady_native_eventPhase},enumerable:!0,configurable:!0}),Object.defineProperty(ne,"__shady_native_eventPhase",oe));function pe(a){function b(c,d){c=new a(c,d);c.__composed=d&&!!d.composed;return c}b.__proto__=a;b.prototype=a.prototype;return b}var qe={focus:!0,blur:!0};function re(a){return a.__target!==a.target||a.__relatedTarget!==a.relatedTarget}
function se(a,b,c){if(c=b.__handlers&&b.__handlers[a.type]&&b.__handlers[a.type][c])for(var d=0,e;(e=c[d])&&(!re(a)||a.target!==a.relatedTarget)&&(e.call(b,a),!a.__immediatePropagationStopped);d++);}var te=(new Event("e")).hasOwnProperty("currentTarget");
function ue(a){a=te?Object.create(a):a;var b=a.composedPath(),c=b.map(function(n){return me(n,b)}),d=a.bubbles,e=Object.getOwnPropertyDescriptor(a,"currentTarget");Object.defineProperty(a,"currentTarget",{configurable:!0,enumerable:!0,get:function(){return k}});var f=Event.CAPTURING_PHASE,g=Object.getOwnPropertyDescriptor(a,"eventPhase");Object.defineProperty(a,"eventPhase",{configurable:!0,enumerable:!0,get:function(){return f}});try{for(var h=b.length-1;h>=0;h--){var k=b[h];f=k===c[h]?Event.AT_TARGET:
Event.CAPTURING_PHASE;se(a,k,"capture");if(a.ta)return}for(h=0;h<b.length;h++){k=b[h];var l=k===c[h];if(l||d)if(f=l?Event.AT_TARGET:Event.BUBBLING_PHASE,se(a,k,"bubble"),a.ta)break}}finally{te||(e?Object.defineProperty(a,"currentTarget",e):delete a.currentTarget,g?Object.defineProperty(a,"eventPhase",g):delete a.eventPhase)}}function ve(a,b,c,d){for(var e=0;e<a.length;e++){var f=a[e],g=f.type,h=f.capture;if(b===f.node&&c===g&&d===h)return e}return-1}
function we(a){Ad();return!G.aa&&this instanceof Node&&!ld(document,this)?(a.__target||xe(a,this),ue(a)):this.__shady_native_dispatchEvent(a)}
function ye(a,b,c){var d=this,e=ge(c),f=e.capture,g=e.once,h=e.jb;e=e.ib;if(b){var k=typeof b;if(k==="function"||k==="object")if(k!=="object"||b.handleEvent&&typeof b.handleEvent==="function"){if(ie[a])return this.__shady_native_addEventListener(a,b,e);var l=h||this;if(h=b[de]){if(ve(h,l,a,f)>-1)return}else b[de]=[];h=function(n){g&&d.__shady_removeEventListener(a,b,c);n.__target||xe(n);if(l!==d){var t=Object.getOwnPropertyDescriptor(n,"currentTarget");Object.defineProperty(n,"currentTarget",{get:function(){return l},
configurable:!0});var D=Object.getOwnPropertyDescriptor(n,"eventPhase");Object.defineProperty(n,"eventPhase",{configurable:!0,enumerable:!0,get:function(){return f?Event.CAPTURING_PHASE:Event.BUBBLING_PHASE}})}n.__previousCurrentTarget=n.currentTarget;if(!I(l)&&l.localName!=="slot"||n.composedPath().indexOf(l)!=-1)if(n.composed||n.composedPath().indexOf(l)>-1)if(re(n)&&n.target===n.relatedTarget)n.eventPhase===Event.BUBBLING_PHASE&&n.stopImmediatePropagation();else if(n.eventPhase===Event.CAPTURING_PHASE||
n.bubbles||n.target===l||l instanceof Window){var wa=k==="function"?b.call(l,n):b.handleEvent&&b.handleEvent(n);l!==d&&(t?(Object.defineProperty(n,"currentTarget",t),t=null):delete n.currentTarget,D?(Object.defineProperty(n,"eventPhase",D),D=null):delete n.eventPhase);return wa}};b[de].push({node:l,type:a,capture:f,Kc:h});this.__handlers=this.__handlers||{};this.__handlers[a]=this.__handlers[a]||{capture:[],bubble:[]};this.__handlers[a][f?"capture":"bubble"].push(h);qe[a]||this.__shady_native_addEventListener(a,
h,e)}}}function ze(a,b,c){if(b){var d=ge(c);c=d.capture;var e=d.jb;d=d.ib;if(ie[a])return this.__shady_native_removeEventListener(a,b,d);var f=e||this;e=void 0;var g=null;try{g=b[de]}catch(h){}g&&(f=ve(g,f,a,c),f>-1&&(e=g.splice(f,1)[0].Kc,g.length||(b[de]=void 0)));this.__shady_native_removeEventListener(a,e||b,d);e&&this.__handlers&&this.__handlers[a]&&(a=this.__handlers[a][c?"capture":"bubble"],b=a.indexOf(e),b>-1&&a.splice(b,1))}}
function Ae(){for(var a in qe)window.__shady_native_addEventListener(a,function(b){b.__target||(xe(b),ue(b))},!0)}var Be=K(ne);function xe(a,b){b=b===void 0?a.target:b;a.__target=b;a.__relatedTarget=a.relatedTarget;if(G.j){b=Object.getPrototypeOf(a);if(!b.hasOwnProperty("__shady_patchedProto")){var c=Object.create(b);c.__shady_sourceProto=b;J(c,Be);b.__shady_patchedProto=c}a.__proto__=b.__shady_patchedProto}else J(a,Be)}var Ce=pe(Event),De=pe(CustomEvent),Ee=pe(MouseEvent);
function Fe(){if(!ee&&Object.getOwnPropertyDescriptor(Event.prototype,"isTrusted")){var a=function(){var b=new MouseEvent("click",{bubbles:!0,cancelable:!0,composed:!0});this.__shady_dispatchEvent(b)};Element.prototype.click?Element.prototype.click=a:HTMLElement.prototype.click&&(HTMLElement.prototype.click=a)}}
var Ge=Object.getOwnPropertyNames(Element.prototype).filter(function(a){return a.substring(0,2)==="on"}),He=Object.getOwnPropertyNames(HTMLElement.prototype).filter(function(a){return a.substring(0,2)==="on"});function Ie(a){return{set:function(b){var c=E(this),d=a.substring(2);c.J||(c.J={});c.J[a]&&this.removeEventListener(d,c.J[a]);this.__shady_addEventListener(d,b);c.J[a]=b},get:function(){var b=F(this);return b&&b.J&&b.J[a]},configurable:!0}};function Je(a,b){return{index:a,ba:[],oa:b}}
function Ke(a,b,c,d){var e=0,f=0,g=0,h=0,k=Math.min(b-e,d-f);if(e==0&&f==0)a:{for(g=0;g<k;g++)if(a[g]!==c[g])break a;g=k}if(b==a.length&&d==c.length){h=a.length;for(var l=c.length,n=0;n<k-g&&Le(a[--h],c[--l]);)n++;h=n}e+=g;f+=g;b-=h;d-=h;if(b-e==0&&d-f==0)return[];if(e==b){for(b=Je(e,0);f<d;)b.ba.push(c[f++]);return[b]}if(f==d)return[Je(e,b-e)];k=e;g=f;d=d-g+1;h=b-k+1;b=Array(d);for(l=0;l<d;l++)b[l]=Array(h),b[l][0]=l;for(l=0;l<h;l++)b[0][l]=l;for(l=1;l<d;l++)for(n=1;n<h;n++)if(a[k+n-1]===c[g+l-1])b[l][n]=
b[l-1][n-1];else{var t=b[l-1][n]+1,D=b[l][n-1]+1;b[l][n]=t<D?t:D}k=b.length-1;g=b[0].length-1;d=b[k][g];for(a=[];k>0||g>0;)k==0?(a.push(2),g--):g==0?(a.push(3),k--):(h=b[k-1][g-1],l=b[k-1][g],n=b[k][g-1],t=l<n?l<h?l:h:n<h?n:h,t==h?(h==d?a.push(0):(a.push(1),d=h),k--,g--):t==l?(a.push(3),k--,d=l):(a.push(2),g--,d=n));a.reverse();b=void 0;k=[];for(g=0;g<a.length;g++)switch(a[g]){case 0:b&&(k.push(b),b=void 0);e++;f++;break;case 1:b||(b=Je(e,0));b.oa++;e++;b.ba.push(c[f]);f++;break;case 2:b||(b=Je(e,
0));b.oa++;e++;break;case 3:b||(b=Je(e,0)),b.ba.push(c[f]),f++}b&&k.push(b);return k}function Le(a,b){return a===b};var Me=K({dispatchEvent:we,addEventListener:ye,removeEventListener:ze});var Ne=null;function O(){Ne||(Ne=window.ShadyCSS&&window.ShadyCSS.ScopingShim);return Ne||null}function Oe(a,b,c){var d=O();return d&&b==="class"?(d.setElementClass(a,c),!0):!1}function Pe(a,b){var c=O();c&&c.unscopeNode(a,b)}function Qe(a,b){var c=O();if(!c)return!0;if(a.nodeType===Node.DOCUMENT_FRAGMENT_NODE){c=!0;for(a=a.__shady_firstChild;a;a=a.__shady_nextSibling)c=c&&Qe(a,b);return c}return a.nodeType!==Node.ELEMENT_NODE?!0:c.currentScopeForNode(a)===b}
function Re(a){if(a.nodeType!==Node.ELEMENT_NODE)return"";var b=O();return b?b.currentScopeForNode(a):""}function Se(a,b){if(a)for(a.nodeType===Node.ELEMENT_NODE&&b(a),a=a.__shady_firstChild;a;a=a.__shady_nextSibling)a.nodeType===Node.ELEMENT_NODE&&Se(a,b)};var Te=window.document,Ue=G.aa,Ve=Object.getOwnPropertyDescriptor(Node.prototype,"isConnected"),We=Ve&&Ve.get;function Xe(a){for(var b;b=a.__shady_firstChild;)a.__shady_removeChild(b)}function Ye(a){var b=F(a);if(b&&b.sa!==void 0)for(b=a.__shady_firstChild;b;b=b.__shady_nextSibling)Ye(b);if(a=F(a))a.sa=void 0}function Ze(a){var b=a;if(a&&a.localName==="slot"){var c=F(a);(c=c&&c.S)&&(b=c.length?c[0]:Ze(a.__shady_nextSibling))}return b}
function $e(a,b,c){if(a=(a=F(a))&&a.Z){if(b)if(b.nodeType===Node.DOCUMENT_FRAGMENT_NODE)for(var d=0,e=b.childNodes.length;d<e;d++)a.addedNodes.push(b.childNodes[d]);else a.addedNodes.push(b);c&&a.removedNodes.push(c);Cd(a)}}
var df=K({get parentNode(){var a=F(this);a=a&&a.parentNode;return a!==void 0?a:this.__shady_native_parentNode},get firstChild(){var a=F(this);a=a&&a.firstChild;return a!==void 0?a:this.__shady_native_firstChild},get lastChild(){var a=F(this);a=a&&a.lastChild;return a!==void 0?a:this.__shady_native_lastChild},get nextSibling(){var a=F(this);a=a&&a.nextSibling;return a!==void 0?a:this.__shady_native_nextSibling},get previousSibling(){var a=F(this);a=a&&a.previousSibling;return a!==void 0?a:this.__shady_native_previousSibling},
get childNodes(){if(H(this)){var a=F(this);if(!a.childNodes){a.childNodes=[];for(var b=this.__shady_firstChild;b;b=b.__shady_nextSibling)a.childNodes.push(b)}var c=a.childNodes}else c=this.__shady_native_childNodes;c.item=function(d){return c[d]};return c},get parentElement(){var a=F(this);(a=a&&a.parentNode)&&a.nodeType!==Node.ELEMENT_NODE&&(a=null);return a!==void 0?a:this.__shady_native_parentElement},get isConnected(){if(We&&We.call(this))return!0;if(this.nodeType==Node.DOCUMENT_FRAGMENT_NODE)return!1;
var a=this.ownerDocument;if(a===null||ld(a,this))return!0;for(a=this;a&&!(a instanceof Document);)a=a.__shady_parentNode||(I(a)?a.host:void 0);return!!(a&&a instanceof Document)},get textContent(){if(H(this)){for(var a=[],b=this.__shady_firstChild;b;b=b.__shady_nextSibling)b.nodeType!==Node.COMMENT_NODE&&a.push(b.__shady_textContent);return a.join("")}return this.__shady_native_textContent},set textContent(a){if(typeof a==="undefined"||a===null)a="";switch(this.nodeType){case Node.ELEMENT_NODE:case Node.DOCUMENT_FRAGMENT_NODE:if(!H(this)&&
G.j){var b=this.__shady_firstChild;(b!=this.__shady_lastChild||b&&b.nodeType!=Node.TEXT_NODE)&&Xe(this);this.__shady_native_textContent=a}else Xe(this),(a.length>0||this.nodeType===Node.ELEMENT_NODE)&&this.__shady_insertBefore(document.createTextNode(a));break;default:this.nodeValue=a}},insertBefore:function(a,b){if(this.ownerDocument!==Te&&a.ownerDocument!==Te)return this.__shady_native_insertBefore(a,b),a;if(a===this)throw Error("Failed to execute 'appendChild' on 'Node': The new child element contains the parent.");
if(b){var c=F(b);c=c&&c.parentNode;if(c!==void 0&&c!==this||c===void 0&&b.__shady_native_parentNode!==this)throw Error("Failed to execute 'insertBefore' on 'Node': The node before which the new node is to be inserted is not a child of this node.");}if(b===a)return a;$e(this,a);var d=[],e=(c=af(this))?c.host.localName:Re(this),f=a.__shady_parentNode;if(f){var g=Re(a);var h=!!c||!af(a)||Ue&&this.__noInsertionPoint!==void 0;f.__shady_removeChild(a,h)}f=!0;var k=(!Ue||a.__noInsertionPoint===void 0&&this.__noInsertionPoint===
void 0)&&!Qe(a,e),l=c&&!a.__noInsertionPoint&&(!Ue||a.nodeType===Node.DOCUMENT_FRAGMENT_NODE);if(l||k)k&&(g=g||Re(a)),Se(a,function(n){l&&n.localName==="slot"&&d.push(n);if(k){var t=g;O()&&(t&&Pe(n,t),(t=O())&&t.scopeNode(n,e))}});d.length&&(c.Ja(d),c.v());H(this)&&(bf(a,this,b),h=F(this),h.root?(f=!1,ed(this)&&h.root.v()):c&&this.localName==="slot"&&(f=!1,c.v()));f?(c=I(this)?this.host:this,b?(b=Ze(b),c.__shady_native_insertBefore(a,b)):c.__shady_native_appendChild(a)):a.ownerDocument!==this.ownerDocument&&
this.ownerDocument.adoptNode(a);return a},appendChild:function(a){if(this!=a||!I(a))return this.__shady_insertBefore(a)},removeChild:function(a,b){b=b===void 0?!1:b;if(this.ownerDocument!==Te)return this.__shady_native_removeChild(a);if(a.__shady_parentNode!==this)throw Error("The node to be removed is not a child of this node: "+a);$e(this,null,a);var c=af(a),d=c&&c.Ub(a),e=F(this);if(H(this)&&(cf(a,this),ed(this))){e.root.v();var f=!0}if(O()&&!b&&c&&a.nodeType!==Node.TEXT_NODE){var g=Re(a);Se(a,
function(h){Pe(h,g)})}Ye(a);c&&((b=this.localName==="slot")&&(f=!0),(d||b)&&c.v());f||(f=I(this)?this.host:this,(!e.root&&a.localName!=="slot"||f===a.__shady_native_parentNode)&&f.__shady_native_removeChild(a));return a},replaceChild:function(a,b){this.__shady_insertBefore(a,b);this.__shady_removeChild(b);return a},cloneNode:function(a){if(this.localName=="template")return this.__shady_native_cloneNode(a);var b=this.__shady_native_cloneNode(!1);if(a&&b.nodeType!==Node.ATTRIBUTE_NODE){a=this.__shady_firstChild;
for(var c;a;a=a.__shady_nextSibling)c=a.__shady_cloneNode(!0),b.__shady_appendChild(c)}return b},getRootNode:function(a){if(this&&this.nodeType){var b=E(this),c=b.sa;c===void 0&&(I(this)?(c=this,b.sa=c):(c=(c=this.__shady_parentNode)?c.__shady_getRootNode(a):this,document.documentElement.__shady_native_contains(this)&&(b.sa=c)));return c}},contains:function(a){return md(this,a)}});var ef=K({get assignedSlot(){var a=this.__shady_parentNode;(a=a&&a.__shady_shadowRoot)&&a.ja();return(a=F(this))&&a.assignedSlot||null}});/*

 Copyright (c) 2022 The Polymer Project Authors
 SPDX-License-Identifier: BSD-3-Clause
*/
var ff=new Map;[["(",{end:")",ra:!0}],["[",{end:"]",ra:!0}],['"',{end:'"',ra:!1}],["'",{end:"'",ra:!1}]].forEach(function(a){var b=p(a);a=b.next().value;b=b.next().value;ff.set(a,b)});function gf(a,b,c,d){for(d=d===void 0?!0:d;b<a.length;b++)if(a[b]==="\\"&&b<a.length-1&&a[b+1]!=="\n")b++;else{if(c.indexOf(a[b])!==-1)return b;if(d&&ff.has(a[b])){var e=ff.get(a[b]);b=gf(a,b+1,[e.end],e.ra)}}return a.length}
function hf(a){function b(){if(d.length>0){for(;d[d.length-1]===" ";)d.pop();c.push({gb:d.filter(function(k,l){return l%2===0}),oc:d.filter(function(k,l){return l%2===1})});d.length=0}}for(var c=[],d=[],e=0;e<a.length;){var f=d[d.length-1],g=gf(a,e,[","," ",">","+","~"]),h=g===e?a[e]:a.substring(e,g);if(h===",")b();else if([void 0," ",">","+","~"].indexOf(f)===-1||h!==" ")f===" "&&[">","+","~"].indexOf(h)!==-1?d[d.length-1]=h:d.push(h);e=g+(g===e?1:0)}b();return c};function jf(a,b,c){var d=[];kf(a,b,c,d);return d}function kf(a,b,c,d){for(a=a.__shady_firstChild;a;a=a.__shady_nextSibling){var e;if(e=a.nodeType===Node.ELEMENT_NODE){e=a;var f=b,g=c,h=d,k=f(e);k&&h.push(e);g&&g(k)?e=k:(kf(e,f,g,h),e=void 0)}if(e)break}}
var lf={get firstElementChild(){var a=F(this);if(a&&a.firstChild!==void 0){for(a=this.__shady_firstChild;a&&a.nodeType!==Node.ELEMENT_NODE;)a=a.__shady_nextSibling;return a}return this.__shady_native_firstElementChild},get lastElementChild(){var a=F(this);if(a&&a.lastChild!==void 0){for(a=this.__shady_lastChild;a&&a.nodeType!==Node.ELEMENT_NODE;)a=a.__shady_previousSibling;return a}return this.__shady_native_lastElementChild},get children(){return H(this)?nd(Array.prototype.filter.call(pd(this),function(a){return a.nodeType===
Node.ELEMENT_NODE})):this.__shady_native_children},get childElementCount(){var a=this.__shady_children;return a?a.length:0}},mf=K((lf.append=function(){this.__shady_insertBefore(ud.apply(null,q(r.apply(0,arguments))),null)},lf.prepend=function(){this.__shady_insertBefore(ud.apply(null,q(r.apply(0,arguments))),this.__shady_firstChild)},lf.replaceChildren=function(){for(var a=r.apply(0,arguments),b;(b=this.__shady_firstChild)!==null;)this.__shady_removeChild(b);this.__shady_insertBefore(ud.apply(null,
q(a)),null)},lf));
function nf(a,b){function c(e,f){return(e===a||f.indexOf(":scope")===-1)&&gd.call(e,f)}var d=hf(b);if(d.length<1)return[];for(b=vd(jf(a,function(){return!0}).map(function(e){return vd(d.map(function(f){var g=f.gb,h=g.length-1;return c(e,g[h])?{target:e,X:f,Y:e,index:h}:[]}))}));b.some(function(e){return e.index>0});)b=vd(b.map(function(e){if(e.index<=0)return e;var f=e.target,g=e.Y,h=e.X;e=e.index-1;var k=h.oc[e],l=h.gb[e];if(k===" "){k=[];for(g=g.__shady_parentElement;g;g=g.__shady_parentElement)c(g,l)&&
k.push({target:f,X:h,Y:g,index:e});return k}if(k===">")return g=g.__shady_parentElement,c(g,l)?{target:f,X:h,Y:g,index:e}:[];if(k==="+")return(g=g.__shady_previousElementSibling)&&c(g,l)?{target:f,X:h,Y:g,index:e}:[];if(k==="~"){k=[];for(g=g.__shady_previousElementSibling;g;g=g.__shady_previousElementSibling)c(g,l)&&k.push({target:f,X:h,Y:g,index:e});return k}throw Error("Unrecognized combinator: '"+k+"'.");}));return wd(b.map(function(e){return e.target}))}
var P=G.querySelectorImplementation,of=K({querySelector:function(a){if(P==="native"){var b=Array.prototype.slice.call((this instanceof ShadowRoot?this.host:this).__shady_native_querySelectorAll(a)),c=this.__shady_getRootNode();b=p(b);for(var d=b.next();!d.done;d=b.next())if(d=d.value,d.__shady_getRootNode()==c)return d;return null}if(P==="selectorEngine")return nf(this,a)[0]||null;if(P===void 0)return jf(this,function(e){return gd.call(e,a)},function(e){return!!e})[0]||null;throw Error("Unrecognized value of ShadyDOM.querySelectorImplementation: '"+
(P+"'"));},querySelectorAll:function(a,b){if(b||P==="native"){b=Array.prototype.slice.call((this instanceof ShadowRoot?this.host:this).__shady_native_querySelectorAll(a));var c=this.__shady_getRootNode();return nd(b.filter(function(d){return d.__shady_getRootNode()==c}))}if(P==="selectorEngine")return nd(nf(this,a));if(P===void 0)return nd(jf(this,function(d){return gd.call(d,a)}));throw Error("Unrecognized value of ShadyDOM.querySelectorImplementation: '"+(P+"'"));}}),pf=G.aa&&!G.noPatch?sd({},mf):
mf;sd(mf,of);/*

Copyright (c) 2020 The Polymer Project Authors. All rights reserved.
This code may only be used under the BSD style license found at http://polymer.github.io/LICENSE.txt
The complete set of authors may be found at http://polymer.github.io/AUTHORS.txt
The complete set of contributors may be found at http://polymer.github.io/CONTRIBUTORS.txt
Code distributed by Google as part of the polymer project is also
subject to an additional IP rights grant found at http://polymer.github.io/PATENTS.txt
*/
var qf=K({after:function(){var a=this.__shady_parentNode;if(a!==null){var b=this.__shady_nextSibling;a.__shady_insertBefore(ud.apply(null,q(r.apply(0,arguments))),b)}},before:function(){var a=this.__shady_parentNode;a!==null&&a.__shady_insertBefore(ud.apply(null,q(r.apply(0,arguments))),this)},remove:function(){var a=this.__shady_parentNode;a!==null&&a.__shady_removeChild(this)},replaceWith:function(){var a=r.apply(0,arguments),b=this.__shady_parentNode;if(b!==null){var c=this.__shady_nextSibling;
b.__shady_removeChild(this);b.__shady_insertBefore(ud.apply(null,q(a)),c)}}});var rf=window.document;function sf(a,b){b==="slot"?(a=a.__shady_parentNode,ed(a)&&F(a).root.v()):a.localName==="slot"&&b==="name"&&(b=af(a))&&(b.kc(a),b.v())}
var tf=K({get previousElementSibling(){var a=F(this);if(a&&a.previousSibling!==void 0){for(a=this.__shady_previousSibling;a&&a.nodeType!==Node.ELEMENT_NODE;)a=a.__shady_previousSibling;return a}return this.__shady_native_previousElementSibling},get nextElementSibling(){var a=F(this);if(a&&a.nextSibling!==void 0){for(a=this.__shady_nextSibling;a&&a.nodeType!==Node.ELEMENT_NODE;)a=a.__shady_nextSibling;return a}return this.__shady_native_nextElementSibling},get slot(){return this.getAttribute("slot")},
set slot(a){this.__shady_setAttribute("slot",a)},get className(){return this.getAttribute("class")||""},set className(a){this.__shady_setAttribute("class",a)},setAttribute:function(a,b){this.ownerDocument!==rf?this.__shady_native_setAttribute(a,b):Oe(this,a,b)||(this.__shady_native_setAttribute(a,b),sf(this,a))},removeAttribute:function(a){this.ownerDocument!==rf?this.__shady_native_removeAttribute(a):Oe(this,a,"")?this.getAttribute(a)===""&&this.__shady_native_removeAttribute(a):(this.__shady_native_removeAttribute(a),
sf(this,a))},toggleAttribute:function(a,b){if(this.ownerDocument!==rf)return this.__shady_native_toggleAttribute(a,b);if(!Oe(this,a,""))return b=this.__shady_native_toggleAttribute(a,b),sf(this,a),b;if(this.getAttribute(a)===""&&!b)return this.__shady_native_toggleAttribute(a,b)}});G.aa||Ge.forEach(function(a){tf[a]=Ie(a)});
var xf=K({attachShadow:function(a){if(!this)throw Error("Must provide a host.");if(!a)throw Error("Not enough arguments.");if(a.shadyUpgradeFragment&&!G.mb){var b=a.shadyUpgradeFragment;b.__proto__=ShadowRoot.prototype;b.Ta(this,a);uf(b,b);a=b.__noInsertionPoint?null:b.querySelectorAll("slot");b.__noInsertionPoint=void 0;a&&a.length&&(b.Ja(a),b.v());b.host.__shady_native_appendChild(b)}else b=new vf(wf,this,a);return this.__CE_shadowRoot=b},get shadowRoot(){var a=F(this);return a&&a.Ac||null}});
sd(tf,xf);var yf=document.implementation.createHTMLDocument("inert"),zf=K({get innerHTML(){return H(this)?Md(this.localName==="template"?this.content:this,pd):this.__shady_native_innerHTML},set innerHTML(a){if(this.localName==="template")this.__shady_native_innerHTML=a;else{Xe(this);var b=this.localName||"div";b=this.namespaceURI&&this.namespaceURI!==yf.namespaceURI?yf.createElementNS(this.namespaceURI,b):yf.createElement(b);for(G.j?b.__shady_native_innerHTML=a:b.innerHTML=a;a=b.__shady_firstChild;)this.__shady_insertBefore(a)}}});var Af=K({blur:function(){var a=F(this);(a=(a=a&&a.root)&&a.activeElement)?a.__shady_blur():this.__shady_native_blur()}});G.aa||He.forEach(function(a){Af[a]=Ie(a)});var Bf=K({assignedNodes:function(a){if(this.localName==="slot"){var b=this.__shady_getRootNode();b&&I(b)&&b.ja();return(b=F(this))?(a&&a.flatten?b.S:b.assignedNodes)||[]:[]}},addEventListener:function(a,b,c){if(this.localName!=="slot"||a==="slotchange")ye.call(this,a,b,c);else{typeof c!=="object"&&(c={capture:!!c});var d=this.__shady_parentNode;if(!d)throw Error("ShadyDOM cannot attach event to slot unless it has a `parentNode`");c.K=this;d.__shady_addEventListener(a,b,c)}},removeEventListener:function(a,
b,c){if(this.localName!=="slot"||a==="slotchange")ze.call(this,a,b,c);else{typeof c!=="object"&&(c={capture:!!c});var d=this.__shady_parentNode;if(!d)throw Error("ShadyDOM cannot attach event to slot unless it has a `parentNode`");c.K=this;d.__shady_removeEventListener(a,b,c)}}});var Cf=K({getElementById:function(a){return a===""?null:jf(this,function(b){return b.id==a},function(b){return!!b})[0]||null}});function Df(a,b){for(var c;b&&!a.has(c=b.__shady_getRootNode());)b=c.host;return b}function Ef(a){var b=new Set;for(b.add(a);I(a)&&a.host;)a=a.host.__shady_getRootNode(),b.add(a);return b}
var Ff="__shady_native_"+dd(),Gf=K({get activeElement(){var a=G.j?document.__shady_native_activeElement:document.activeElement;if(!a||!a.nodeType)return null;var b=!!I(this);if(!(this===document||b&&this.host!==a&&this.host.__shady_native_contains(a)))return null;for(b=af(a);b&&b!==this;)a=b.host,b=af(a);return this===document?b?null:a:b===this?a:null},elementsFromPoint:function(a,b){a=document[Ff](a,b);if(this===document&&G.useNativeDocumentEFP)return a;a=[].slice.call(a);b=Ef(this);for(var c=new Set,
d=0;d<a.length;d++)c.add(Df(b,a[d]));var e=[];c.forEach(function(f){return e.push(f)});return e},elementFromPoint:function(a,b){return this===document&&G.useNativeDocumentEFP?this.__shady_native_elementFromPoint(a,b):this.__shady_elementsFromPoint(a,b)[0]||null}});var Hf=window.document,If=K({importNode:function(a,b){if(a.ownerDocument!==Hf||a.localName==="template")return this.__shady_native_importNode(a,b);var c=this.__shady_native_importNode(a,!1);if(b)for(a=a.__shady_firstChild;a;a=a.__shady_nextSibling)b=this.__shady_importNode(a,!0),c.__shady_appendChild(b);return c}});var Jf=K({dispatchEvent:we,addEventListener:ye.bind(window),removeEventListener:ze.bind(window)});var Kf={};Object.getOwnPropertyDescriptor(HTMLElement.prototype,"parentElement")&&(Kf.parentElement=df.parentElement);Object.getOwnPropertyDescriptor(HTMLElement.prototype,"contains")&&(Kf.contains=df.contains);Object.getOwnPropertyDescriptor(HTMLElement.prototype,"children")&&(Kf.children=mf.children);Object.getOwnPropertyDescriptor(HTMLElement.prototype,"innerHTML")&&(Kf.innerHTML=zf.innerHTML);Object.getOwnPropertyDescriptor(HTMLElement.prototype,"className")&&(Kf.className=tf.className);
var Q={EventTarget:[Me],Node:[df,window.EventTarget?null:Me],Text:[ef],Comment:[ef],CDATASection:[ef],ProcessingInstruction:[ef],Element:[tf,mf,qf,ef,!G.j||"innerHTML"in Element.prototype?zf:null,window.HTMLSlotElement?null:Bf],HTMLElement:[Af,Kf],HTMLSlotElement:[Bf],DocumentFragment:[pf,Cf],Document:[If,pf,Cf,Gf],Window:[Jf],CharacterData:[qf],XMLHttpRequest:[window.EventTarget?null:Me]},Lf=G.j?null:["innerHTML","textContent"];
function R(a,b,c,d){b.forEach(function(e){return a&&e&&J(a,e,c,d)})}function Mf(a){var b=a?null:Lf,c;for(c in Q)R(window[c]&&window[c].prototype,Q[c],a,b)}["Text","Comment","CDATASection","ProcessingInstruction"].forEach(function(a){var b=window[a],c=Object.create(b.prototype);c.__shady_protoIsPatched=!0;R(c,Q.EventTarget);R(c,Q.Node);Q[a]&&R(c,Q[a]);b.prototype.__shady_patchedProto=c});
function Nf(a){a.__shady_protoIsPatched=!0;R(a,Q.EventTarget);R(a,Q.Node);R(a,Q.Element);R(a,Q.HTMLElement);R(a,Q.HTMLSlotElement);return a};var Of=G.Ga,Pf=G.j;function Qf(a,b){if(Of&&!a.__shady_protoIsPatched&&!I(a)){var c=Object.getPrototypeOf(a),d=c.hasOwnProperty("__shady_patchedProto")&&c.__shady_patchedProto;d||(d=Object.create(c),Nf(d),c.__shady_patchedProto=d);Object.setPrototypeOf(a,d)}Pf||(b===1?be(a):b===2&&ce(a))}
function Rf(a,b,c,d){Qf(a,1);d=d||null;var e=E(a),f=d?E(d):null;e.previousSibling=d?f.previousSibling:b.__shady_lastChild;if(f=F(e.previousSibling))f.nextSibling=a;if(f=F(e.nextSibling=d))f.previousSibling=a;e.parentNode=b;d?d===c.firstChild&&(c.firstChild=a):(c.lastChild=a,c.firstChild||(c.firstChild=a));c.childNodes=null}
function bf(a,b,c){Qf(b,2);var d=E(b);d.firstChild!==void 0&&(d.childNodes=null);if(a.nodeType===Node.DOCUMENT_FRAGMENT_NODE)for(a=a.__shady_native_firstChild;a;a=a.__shady_native_nextSibling)Rf(a,b,d,c);else Rf(a,b,d,c)}
function cf(a,b){var c=E(a);b=E(b);a===b.firstChild&&(b.firstChild=c.nextSibling);a===b.lastChild&&(b.lastChild=c.previousSibling);a=c.previousSibling;var d=c.nextSibling;a&&(E(a).nextSibling=d);d&&(E(d).previousSibling=a);c.parentNode=c.previousSibling=c.nextSibling=void 0;b.childNodes!==void 0&&(b.childNodes=null)}
function uf(a,b){var c=E(a);if(b||c.firstChild===void 0){c.childNodes=null;var d=c.firstChild=a.__shady_native_firstChild;c.lastChild=a.__shady_native_lastChild;Qf(a,2);c=d;for(d=void 0;c;c=c.__shady_native_nextSibling){var e=E(c);e.parentNode=b||a;e.nextSibling=c.__shady_native_nextSibling;e.previousSibling=d||null;d=c;Qf(c,1)}}};var Sf=K({addEventListener:function(a,b,c){typeof c!=="object"&&(c={capture:!!c});c.K=c.K||this;this.host.__shady_addEventListener(a,b,c)},removeEventListener:function(a,b,c){typeof c!=="object"&&(c={capture:!!c});c.K=c.K||this;this.host.__shady_removeEventListener(a,b,c)}});function Tf(a,b){J(a,Sf,b);J(a,Gf,b);J(a,zf,b);J(a,mf,b);G.noPatch&&!b?(J(a,df,b),J(a,Cf,b)):G.j||(J(a,Zd),J(a,Xd),J(a,Yd))};var wf={},S=G.deferConnectionCallbacks&&document.readyState==="loading",Uf;function Vf(a){var b=[];do b.unshift(a);while(a=a.__shady_parentNode);return b}function vf(a,b,c){if(a!==wf)throw new TypeError("Illegal constructor");this.g=null;this.Ta(b,c)}m=vf.prototype;
m.Ta=function(a,b){this.host=a;this.mode=b&&b.mode;uf(this.host);a=E(this.host);a.root=this;a.Ac=this.mode!=="closed"?this:null;a=E(this);a.firstChild=a.lastChild=a.parentNode=a.nextSibling=a.previousSibling=null;if(G.preferPerformance)for(;a=this.host.__shady_native_firstChild;)this.host.__shady_native_removeChild(a);else this.v()};m.v=function(){var a=this;this.P||(this.P=!0,zd(function(){return a.ja()}))};m.Ib=function(){for(var a,b=this;b;)b.P&&(a=b),b=b.Hb();return a};
m.Hb=function(){var a=this.host.__shady_getRootNode();if(I(a)){var b=F(this.host);if(b&&b.ea>0)return a}};m.ja=function(){var a=this.P&&this.Ib();a&&a._renderSelf()};m.Db=function(){!this.Sa&&this.P&&this.ja()};
m._renderSelf=function(){var a=S;S=!0;this.P=!1;this.g&&(this.yb(),this.wb());if(!G.preferPerformance&&!this.Sa)for(var b=this.host.__shady_firstChild;b;b=b.__shady_nextSibling){var c=F(b);b.__shady_native_parentNode!==this.host||b.localName!=="slot"&&c.assignedSlot||this.host.__shady_native_removeChild(b)}this.Sa=!0;S=a;Uf&&Uf()};
m.yb=function(){this.na();for(var a=0,b;a<this.g.length;a++)b=this.g[a],this.ub(b);for(a=this.host.__shady_firstChild;a;a=a.__shady_nextSibling)this.Ma(a);for(a=0;a<this.g.length;a++){b=this.g[a];var c=F(b);if(!c.assignedNodes.length)for(var d=b.__shady_firstChild;d;d=d.__shady_nextSibling)this.Ma(d,b);(d=(d=F(b.__shady_parentNode))&&d.root)&&(d.Ra()||d.P)&&d._renderSelf();this.Ia(c.S,c.assignedNodes);if(d=c.Xa){for(var e=0;e<d.length;e++)F(d[e]).za=null;c.Xa=null;d.length>c.assignedNodes.length&&
(c.Ea=!0)}c.Ea&&(c.Ea=!1,this.Oa(b))}};m.Ma=function(a,b){var c=E(a),d=c.za;c.za=null;b||(b=(b=this.i[a.__shady_slot||"__catchall"])&&b[0]);b?(E(b).assignedNodes.push(a),c.assignedSlot=b):c.assignedSlot=void 0;d!==c.assignedSlot&&c.assignedSlot&&(E(c.assignedSlot).Ea=!0)};m.ub=function(a){var b=F(a),c=b.assignedNodes;b.assignedNodes=[];b.S=[];if(b.Xa=c)for(b=0;b<c.length;b++){var d=F(c[b]);d.za=d.assignedSlot;d.assignedSlot===a&&(d.assignedSlot=null)}};
m.Ia=function(a,b){for(var c=0,d=void 0;c<b.length&&(d=b[c]);c++)if(d.localName=="slot"){var e=F(d).assignedNodes;e&&e.length&&this.Ia(a,e)}else a.push(b[c])};m.Oa=function(a){a.__shady_native_dispatchEvent(new Event("slotchange"));a=F(a);a.assignedSlot&&this.Oa(a.assignedSlot)};m.wb=function(){for(var a=this.g,b=[],c=0;c<a.length;c++){var d=a[c].__shady_parentNode,e=F(d);e&&e.root||!(b.indexOf(d)<0)||b.push(d)}for(a=0;a<b.length;a++)c=b[a],this.jc(c===this?this.host:c,this.xb(c))};
m.xb=function(a){var b=[];for(a=a.__shady_firstChild;a;a=a.__shady_nextSibling)if(this.Lb(a))for(var c=F(a).S,d=0;d<c.length;d++)b.push(c[d]);else b.push(a);return b};m.Lb=function(a){return a.localName=="slot"};
m.jc=function(a,b){for(var c=od(a),d=Ke(b,b.length,c,c.length),e=0,f=0,g=void 0;e<d.length&&(g=d[e]);e++){for(var h=0,k=void 0;h<g.ba.length&&(k=g.ba[h]);h++)k.__shady_native_parentNode===a&&a.__shady_native_removeChild(k),c.splice(g.index+f,1);f-=g.oa}e=0;for(f=void 0;e<d.length&&(f=d[e]);e++)for(g=c[f.index],h=f.index;h<f.index+f.oa;h++)k=b[h],a.__shady_native_insertBefore(k,g),c.splice(h,0,k)};m.Cb=function(){this.I=this.I||[];this.g=this.g||[];this.i=this.i||{}};
m.Ja=function(a){this.Cb();this.I.push.apply(this.I,q(a))};m.na=function(){this.I&&this.I.length&&(this.Pb(this.I),this.I=[])};m.Pb=function(a){for(var b,c=0;c<a.length;c++){var d=a[c];uf(d);var e=d.__shady_parentNode;uf(e);e=F(e);e.ea=(e.ea||0)+1;e=this.Va(d);this.i[e]?(b=b||{},b[e]=!0,this.i[e].push(d)):this.i[e]=[d];this.g.push(d)}if(b)for(var f in b)this.i[f]=this.ab(this.i[f])};m.Va=function(a){var b=a.name||a.getAttribute("name")||"__catchall";return a.qb=b};
m.ab=function(a){return a.sort(function(b,c){b=Vf(b);for(var d=Vf(c),e=0;e<b.length;e++){c=b[e];var f=d[e];if(c!==f)return b=pd(c.__shady_parentNode),b.indexOf(c)-b.indexOf(f)}})};m.Ub=function(a){if(this.g){this.na();var b=this.i,c;for(c in b)for(var d=b[c],e=0;e<d.length;e++){var f=d[e];if(md(a,f)){d.splice(e,1);var g=this.g.indexOf(f);g>=0&&(this.g.splice(g,1),(g=F(f.__shady_parentNode))&&g.ea&&g.ea--);e--;this.Vb(f);g=!0}}return g}};
m.kc=function(a){if(this.g){this.na();var b=a.qb,c=this.Va(a);if(c!==b){b=this.i[b];var d=b.indexOf(a);d>=0&&b.splice(d,1);b=this.i[c]||(this.i[c]=[]);b.push(a);b.length>1&&(this.i[c]=this.ab(b))}}};m.Vb=function(a){a=F(a);var b=a.S;if(b)for(var c=0;c<b.length;c++){var d=b[c],e=d.__shady_native_parentNode;e&&e.__shady_native_removeChild(d)}a.S=[];a.assignedNodes=[]};m.Ra=function(){this.na();return!(!this.g||!this.g.length)};
(function(a){a.__proto__=DocumentFragment.prototype;Tf(a,"__shady_");Tf(a);Object.defineProperties(a,{nodeType:{value:Node.DOCUMENT_FRAGMENT_NODE,configurable:!0},nodeName:{value:"#document-fragment",configurable:!0},nodeValue:{value:null,configurable:!0}});["localName","namespaceURI","prefix"].forEach(function(b){Object.defineProperty(a,b,{value:void 0,configurable:!0})});["ownerDocument","baseURI","isConnected"].forEach(function(b){Object.defineProperty(a,b,{get:function(){return this.host[b]},
configurable:!0})})})(vf.prototype);
if(window.customElements&&window.customElements.define&&G.inUse&&!G.preferPerformance){var Wf=new Map;Uf=function(){var a=[];Wf.forEach(function(d,e){a.push([e,d])});Wf.clear();for(var b=0;b<a.length;b++){var c=a[b][0];a[b][1]?c.__shadydom_connectedCallback():c.__shadydom_disconnectedCallback()}};S&&document.addEventListener("readystatechange",function(){S=!1;Uf()},{once:!0});var Xf=function(a,b,c){var d=0,e="__isConnected"+d++;if(b||c)a.prototype.connectedCallback=a.prototype.__shadydom_connectedCallback=
function(){S?Wf.set(this,!0):this[e]||(this[e]=!0,b&&b.call(this))},a.prototype.disconnectedCallback=a.prototype.__shadydom_disconnectedCallback=function(){S?this.isConnected||Wf.set(this,!1):this[e]&&(this[e]=!1,c&&c.call(this))};return a},Yf=window.customElements.define,Zf=function(a,b){var c=b.prototype.connectedCallback,d=b.prototype.disconnectedCallback;Yf.call(window.customElements,a,Xf(b,c,d));b.prototype.connectedCallback=c;b.prototype.disconnectedCallback=d};window.customElements.define=
Zf;Object.defineProperty(window.CustomElementRegistry.prototype,"define",{value:Zf,configurable:!0})}function af(a){a=a.__shady_getRootNode();if(I(a))return a};function $f(a){this.node=a}m=$f.prototype;m.addEventListener=function(a,b,c){return this.node.__shady_addEventListener(a,b,c)};m.removeEventListener=function(a,b,c){return this.node.__shady_removeEventListener(a,b,c)};m.appendChild=function(a){return this.node.__shady_appendChild(a)};m.insertBefore=function(a,b){return this.node.__shady_insertBefore(a,b)};m.removeChild=function(a){return this.node.__shady_removeChild(a)};m.replaceChild=function(a,b){return this.node.__shady_replaceChild(a,b)};
m.cloneNode=function(a){return this.node.__shady_cloneNode(a)};m.getRootNode=function(a){return this.node.__shady_getRootNode(a)};m.contains=function(a){return this.node.__shady_contains(a)};m.dispatchEvent=function(a){return this.node.__shady_dispatchEvent(a)};m.setAttribute=function(a,b){this.node.__shady_setAttribute(a,b)};m.getAttribute=function(a){return this.node.__shady_native_getAttribute(a)};m.hasAttribute=function(a){return this.node.__shady_native_hasAttribute(a)};m.removeAttribute=function(a){this.node.__shady_removeAttribute(a)};
m.toggleAttribute=function(a,b){return this.node.__shady_toggleAttribute(a,b)};m.attachShadow=function(a){return this.node.__shady_attachShadow(a)};m.focus=function(){this.node.__shady_native_focus()};m.blur=function(){this.node.__shady_blur()};m.importNode=function(a,b){if(this.node.nodeType===Node.DOCUMENT_NODE)return this.node.__shady_importNode(a,b)};m.getElementById=function(a){if(this.node.nodeType===Node.DOCUMENT_NODE)return this.node.__shady_getElementById(a)};
m.elementsFromPoint=function(a,b){return this.node.__shady_elementsFromPoint(a,b)};m.elementFromPoint=function(a,b){return this.node.__shady_elementFromPoint(a,b)};m.querySelector=function(a){return this.node.__shady_querySelector(a)};m.querySelectorAll=function(a,b){return this.node.__shady_querySelectorAll(a,b)};m.assignedNodes=function(a){if(this.node.localName==="slot")return this.node.__shady_assignedNodes(a)};m.append=function(){return this.node.__shady_append.apply(this.node,q(r.apply(0,arguments)))};
m.prepend=function(){return this.node.__shady_prepend.apply(this.node,q(r.apply(0,arguments)))};m.replaceChildren=function(){return this.node.__shady_replaceChildren.apply(this.node,q(r.apply(0,arguments)))};m.after=function(){return this.node.__shady_after.apply(this.node,q(r.apply(0,arguments)))};m.before=function(){return this.node.__shady_before.apply(this.node,q(r.apply(0,arguments)))};m.remove=function(){return this.node.__shady_remove()};
m.replaceWith=function(){return this.node.__shady_replaceWith.apply(this.node,q(r.apply(0,arguments)))};
ca.Object.defineProperties($f.prototype,{activeElement:{configurable:!0,enumerable:!0,get:function(){if(I(this.node)||this.node.nodeType===Node.DOCUMENT_NODE)return this.node.__shady_activeElement}},_activeElement:{configurable:!0,enumerable:!0,get:function(){return this.activeElement}},host:{configurable:!0,enumerable:!0,get:function(){if(I(this.node))return this.node.host}},parentNode:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_parentNode}},firstChild:{configurable:!0,
enumerable:!0,get:function(){return this.node.__shady_firstChild}},lastChild:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_lastChild}},nextSibling:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_nextSibling}},previousSibling:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_previousSibling}},childNodes:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_childNodes}},parentElement:{configurable:!0,enumerable:!0,
get:function(){return this.node.__shady_parentElement}},firstElementChild:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_firstElementChild}},lastElementChild:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_lastElementChild}},nextElementSibling:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_nextElementSibling}},previousElementSibling:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_previousElementSibling}},
children:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_children}},childElementCount:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_childElementCount}},shadowRoot:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_shadowRoot}},assignedSlot:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_assignedSlot}},isConnected:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_isConnected}},innerHTML:{configurable:!0,
enumerable:!0,get:function(){return this.node.__shady_innerHTML},set:function(a){this.node.__shady_innerHTML=a}},textContent:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_textContent},set:function(a){this.node.__shady_textContent=a}},slot:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_slot},set:function(a){this.node.__shady_slot=a}},className:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_className},set:function(a){this.node.__shady_className=
a}}});function ag(a){Object.defineProperty($f.prototype,a,{get:function(){return this.node["__shady_"+a]},set:function(b){this.node["__shady_"+a]=b},configurable:!0})}Ge.forEach(function(a){return ag(a)});He.forEach(function(a){return ag(a)});var bg=new WeakMap;function cg(a){if(I(a)||a instanceof $f)return a;var b=bg.get(a);b||(b=new $f(a),bg.set(a,b));return b};if(G.inUse){var dg=G.j?function(a){return a}:function(a){ce(a);be(a);return a};window.ShadyDOM={inUse:G.inUse,patch:dg,isShadyRoot:I,enqueue:zd,flush:Ad,flushInitial:function(a){a.Db()},settings:G,filterMutations:Fd,observeChildren:Dd,unobserveChildren:Ed,deferConnectionCallbacks:G.deferConnectionCallbacks,preferPerformance:G.preferPerformance,handlesDynamicScoping:!0,wrap:G.noPatch?cg:dg,wrapIfNeeded:G.noPatch===!0?cg:function(a){return a},Wrapper:$f,composedPath:le,noPatch:G.noPatch,patchOnDemand:G.Ga,
nativeMethods:Od,nativeTree:Pd,patchElementProto:Nf,querySelectorImplementation:G.querySelectorImplementation};Wd();Mf("__shady_");Object.defineProperty(document,"_activeElement",Gf.activeElement);J(Window.prototype,Jf,"__shady_");G.noPatch?G.Ga&&J(Element.prototype,xf):(Mf(),Fe());Ae();window.Event=Ce;window.CustomEvent=De;window.MouseEvent=Ee;window.ShadowRoot=vf};/*

Copyright (c) 2017 The Polymer Project Authors. All rights reserved.
This code may only be used under the BSD style license found at http://polymer.github.io/LICENSE.txt
The complete set of authors may be found at http://polymer.github.io/AUTHORS.txt
The complete set of contributors may be found at http://polymer.github.io/CONTRIBUTORS.txt
Code distributed by Google as part of the polymer project is also
subject to an additional IP rights grant found at http://polymer.github.io/PATENTS.txt
*/
function eg(){this.end=this.start=0;this.rules=this.parent=this.previous=null;this.cssText=this.parsedCssText="";this.atRule=!1;this.type=0;this.parsedSelector=this.selector=this.keyframesName=""}
function fg(a){var b=a=a.replace(gg,"").replace(hg,""),c=new eg;c.start=0;c.end=b.length;for(var d=c,e=0,f=b.length;e<f;e++)if(b[e]==="{"){d.rules||(d.rules=[]);var g=d,h=g.rules[g.rules.length-1]||null;d=new eg;d.start=e+1;d.parent=g;d.previous=h;g.rules.push(d)}else b[e]==="}"&&(d.end=e+1,d=d.parent||c);return ig(c,a)}
function ig(a,b){var c=b.substring(a.start,a.end-1);a.parsedCssText=a.cssText=c.trim();a.parent&&(c=b.substring(a.previous?a.previous.end:a.parent.start,a.start-1),c=_expandUnicodeEscapes$$module$third_party$javascript$polymer$v2$shadycss$src$css_parse(c),c=c.replace(jg," "),c=c.substring(c.lastIndexOf(";")+1),c=a.parsedSelector=a.selector=c.trim(),a.atRule=c.indexOf("@")===0,a.atRule?c.indexOf("@media")===0?a.type=4:c.match(kg)&&(a.type=7,a.keyframesName=a.selector.split(jg).pop()):a.type=c.indexOf("--")===
0?1E3:1);if(c=a.rules)for(var d=0,e=c.length,f=void 0;d<e&&(f=c[d]);d++)ig(f,b);return a}function _expandUnicodeEscapes$$module$third_party$javascript$polymer$v2$shadycss$src$css_parse(a){return a.replace(/\\([0-9a-f]{1,6})\s/gi,function(){for(var b=arguments[1],c=6-b.length;c--;)b="0"+b;return"\\"+b})}
function lg(a,b,c){c=c===void 0?"":c;var d="";if(a.cssText||a.rules){var e=a.rules;if(e&&!_hasMixinRules$$module$third_party$javascript$polymer$v2$shadycss$src$css_parse(e))for(var f=0,g=e.length,h=void 0;f<g&&(h=e[f]);f++)d=lg(h,b,d);else b?b=a.cssText:(b=a.cssText,b=b.replace(mg,"").replace(ng,""),b=b.replace(og,"").replace(pg,"")),(d=b.trim())&&(d="  "+d+"\n")}d&&(a.selector&&(c+=a.selector+" {\n"),c+=d,a.selector&&(c+="}\n\n"));return c}
function _hasMixinRules$$module$third_party$javascript$polymer$v2$shadycss$src$css_parse(a){a=a[0];return!!a&&!!a.selector&&a.selector.indexOf("--")===0}var gg=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//gim,hg=/@import[^;]*;/gim,mg=/(?:^[^;\-\s}]+)?--[^;{}]*?:[^{};]*?(?:[;\n]|$)/gim,ng=/(?:^[^;\-\s}]+)?--[^;{}]*?:[^{};]*?{[^}]*?}(?:[;\n]|$)?/gim,og=/@apply\s*\(?[^);]*\)?\s*(?:[;\n]|$)?/gim,pg=/[^;:]*?:[^;]*?var\([^;]*\)(?:[;\n]|$)?/gim,kg=/^@[^\s]*keyframes/,jg=/\s+/g;var T=!(window.ShadyDOM&&window.ShadyDOM.inUse),qg;function rg(a){qg=a&&a.shimcssproperties?!1:T||!(navigator.userAgent.match(/AppleWebKit\/601|Edge\/15/)||!window.CSS||!CSS.supports||!CSS.supports("box-shadow","0 0 0 var(--foo)"))}var sg;window.ShadyCSS&&window.ShadyCSS.cssBuild!==void 0&&(sg=window.ShadyCSS.cssBuild);var tg=!(!window.ShadyCSS||!window.ShadyCSS.disableRuntime);
window.ShadyCSS&&window.ShadyCSS.nativeCss!==void 0?qg=window.ShadyCSS.nativeCss:window.ShadyCSS?(rg(window.ShadyCSS),window.ShadyCSS=void 0):rg(window.WebComponents&&window.WebComponents.flags);var U=qg;var ug=/(?:^|[;\s{]\s*)(--[\w-]*?)\s*:\s*(?:((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};{])+)|\{([^}]*)\}(?:(?=[;\s}])|$))/gi,vg=/(?:^|\W+)@apply\s*\(?([^);\n]*)\)?/gi,wg=/(--[\w-]+)\s*([:,;)]|$)/gi,xg=/(animation\s*:)|(animation-name\s*:)/,yg=/@media\s(.*)/,zg=/\{[^}]*\}/g;var Ag=new Set;function Bg(a,b){if(!a)return"";typeof a==="string"&&(a=fg(a));b&&Cg(a,b);return lg(a,U)}function Dg(a){!a.__cssRules&&a.textContent&&(a.__cssRules=fg(a.textContent));return a.__cssRules||null}function Eg(a){return!!a.parent&&a.parent.type===7}function Cg(a,b,c,d){if(a){var e=!1,f=a.type;if(d&&f===4){var g=a.selector.match(yg);g&&(window.matchMedia(g[1]).matches||(e=!0))}f===1?b(a):c&&f===7?c(a):f===1E3&&(e=!0);if((a=a.rules)&&!e)for(e=0,f=a.length,g=void 0;e<f&&(g=a[e]);e++)Cg(g,b,c,d)}}
function Fg(a,b,c,d){var e=document.createElement("style");b&&e.setAttribute("scope",b);e.textContent=a;if(window.enableHotReplacement&&(a=document.head.querySelector("style[scope="+b+"]")))return a.parentElement.replaceChild(e,a),e;Gg(e,c,d);return e}var V=null;function Hg(a){a=document.createComment(" Shady DOM styles for "+a+" ");var b=document.head;b.insertBefore(a,(V?V.nextSibling:null)||b.firstChild);return V=a}
function Gg(a,b,c){b=b||document.head;b.insertBefore(a,c&&c.nextSibling||b.firstChild);V?a.compareDocumentPosition(V)===Node.DOCUMENT_POSITION_PRECEDING&&(V=a):V=a}function Ig(a,b){for(var c=0,d=a.length;b<d;b++)if(a[b]==="(")c++;else if(a[b]===")"&&--c===0)return b;return-1}
function Jg(a,b){var c=a.indexOf("var(");if(c===-1)return b(a,"","","");var d=Ig(a,c+3),e=a.substring(c+4,d);c=a.substring(0,c);a=Jg(a.substring(d+1),b);d=e.indexOf(",");return d===-1?b(c,e.trim(),"",a):b(c,e.substring(0,d).trim(),e.substring(d+1).trim(),a)}function Kg(a,b){T?a.setAttribute("class",b):window.ShadyDOM.nativeMethods.setAttribute.call(a,"class",b)}var Lg=window.ShadyDOM&&window.ShadyDOM.wrap||function(a){return a};
function Mg(a){var b=a.localName,c="";b?b.indexOf("-")>-1||(c=b,b=a.getAttribute&&a.getAttribute("is")||""):(b=a.is,c=a.extends);return{is:b,ca:c}}function Ng(a){for(var b=[],c="",d=0;d>=0&&d<a.length;d++)if(a[d]==="("){var e=Ig(a,d);c+=a.slice(d,e+1);d=e}else a[d]===","?(b.push(c),c=""):c+=a[d];c&&b.push(c);return b}
function Og(a){if(sg!==void 0)return sg;if(a.__cssBuild===void 0){var b=a.getAttribute("css-build");if(b)a.__cssBuild=b;else{a:{b=a.localName==="template"?a.content.firstChild:a.firstChild;if(b instanceof Comment&&(b=b.textContent.trim().split(":"),b[0]==="css-build")){b=b[1];break a}b=""}if(b!==""){var c=a.localName==="template"?a.content.firstChild:a.firstChild;c.parentNode.removeChild(c)}a.__cssBuild=b}}return a.__cssBuild||""}
function Pg(a){a=a===void 0?"":a;return a!==""&&U?T?a==="shadow":a==="shady":!1};function Qg(a,b){var c=window.shadyCSSStyleTransformHooks;return c&&(c=c.didTransformSelector,typeof c==="function")?c(a,b):a}function Rg(){}function Sg(a,b){var c=W;c.la(a,function(d){c.element(d,b||"")})}m=Rg.prototype;m.la=function(a,b){a.nodeType===Node.ELEMENT_NODE&&b(a);if(a=a.localName==="template"?(a.content||a._content||a).childNodes:a.children||a.childNodes)for(var c=0;c<a.length;c++)this.la(a[c],b)};
m.element=function(a,b,c){if(b)if(a.classList)c?(a.classList.remove("style-scope"),a.classList.remove(b)):(a.classList.add("style-scope"),a.classList.add(b));else if(a.getAttribute){var d=a.getAttribute("class");c?d&&(b=d.replace("style-scope","").replace(b,""),Kg(a,b)):Kg(a,(d?d+" ":"")+"style-scope "+b)}};function Tg(a,b,c){var d=W;d.la(a,function(e){d.element(e,b,!0);d.element(e,c)})}function Ug(a,b){var c=W;c.la(a,function(d){c.element(d,b||"",!0)})}
function Vg(a,b,c,d,e){var f=W;e=e===void 0?"":e;e===""&&(T||(d===void 0?"":d)==="shady"?e=Bg(b,c):(a=Mg(a),e=Wg(f,b,a.is,a.ca,c)+"\n\n"));return e.trim()}function Wg(a,b,c,d,e){var f=a.ua(c,d);c=a.Ka(c);return Bg(b,function(g){g.vc||(a.bb(g,a.Ba,c,f),g.vc=!0);e&&e(g,c,f)})}m.Ka=function(a){return a?"."+a:""};m.ua=function(a,b){return b?"[is="+a+"]":a};m.bb=function(a,b,c,d){a.selector=a.o=this.cb(a,b,c,d)};
m.cb=function(a,b,c,d){var e=Ng(a.selector);if(!Eg(a)){a=0;for(var f=e.length,g=void 0;a<f&&(g=e[a]);a++)e[a]=b.call(this,g,c,d)}return e.filter(function(h){return!!h}).join(",")};m.fb=function(a){return a.replace(Xg,function(b,c,d){d.indexOf("+")>-1?d=d.replace(/\+/g,"___"):d.indexOf("___")>-1&&(d=d.replace(/___/g,"+"));return":"+c+"("+d+")"})};
m.Tb=function(a){for(var b=[],c;c=a.match(Yg);){var d=c.index,e=Ig(a,d);if(e===-1)throw Error(c.input+" selector missing ')'");c=a.slice(d,e+1);a=a.replace(c,"\ue000");b.push(c)}return{Ha:a,matches:b}};m.Xb=function(a,b){var c=a.split("\ue000");return b.reduce(function(d,e,f){return d+e+c[f+1]},c[0])};
m.Ba=function(a,b,c){var d=this,e=!1;a=a.trim();var f=Xg.test(a);f&&(a=a.replace(Xg,function(k,l,n){return":"+l+"("+n.replace(/\s/g,"")+")"}),a=this.fb(a));var g=Yg.test(a);if(g){var h=this.Tb(a);a=h.Ha;h=h.matches}a=a.replace(Zg,":host $1");a=a.replace($g,function(k,l,n){e||(k=d.ec(n,l,b,c),e=e||k.stop,l=k.nc,n=k.value);return l+n});g&&(a=this.Xb(a,h));f&&(a=this.fb(a));a=a.replace(ah,function(k,l,n,t){return'[dir="'+n+'"] '+l+t+", "+l+'[dir="'+n+'"]'+t});return Qg(a,b)};
m.ec=function(a,b,c,d){var e=a.indexOf("::slotted");a.indexOf(":host")>=0?a=this.hc(a,d):e!==0&&(a=c?this.eb(a,c):a);c=!1;e>=0&&(b="",c=!0);if(c){var f=!0;c&&(a=a.replace(bh,function(g,h){return" > "+h}))}return{value:a,nc:b,stop:f}};m.eb=function(a,b){a=a.split(/(\[.+?\])/);for(var c=[],d=0;d<a.length;d++)if(d%2===1)c.push(a[d]);else{var e=a[d];if(e!==""||d!==a.length-1)e=e.split(":"),e[0]+=b,c.push(e.join(":"))}return c.join("")};
m.hc=function(a,b){var c=a.match(ch);return(c=c&&c[2].trim()||"")?c[0].match(dh)?a.replace(ch,function(d,e,f){return b+f}):c.split(dh)[0]===b?c:"should_not_match":a.replace(":host",b)};function eh(a){a.selector===":root"&&(a.selector="html")}m.fc=function(a){return a.match(":host")?"":a.match("::slotted")?this.Ba(a,":not(.style-scope)"):Qg(this.eb(a.trim(),":not(.style-scope)"),":not(.style-scope)")};ca.Object.defineProperties(Rg.prototype,{da:{configurable:!0,enumerable:!0,get:function(){return"style-scope"}}});
var Xg=/:(nth[-\w]+)\(([^)]+)\)/,$g=/(^|[\s>+~]+)((?:\[.+?\]|[^\s>+~=[])+)/g,dh=/[[.:#*]/,Zg=RegExp("^(::slotted)"),ch=/(:host)(?:\(((?:\([^)(]*\)|[^)(]*)+?)\))/,bh=/(?:::slotted)(?:\(((?:\([^)(]*\)|[^)(]*)+?)\))/,ah=/(.*):dir\((?:(ltr|rtl))\)(.*)/,Yg=/:(?:matches|any|-(?:webkit|moz)-any)/,W=new Rg;function fh(a,b,c,d,e){this.B=a||null;this.placeholder=b||null;this.Fa=c||[];this.T=null;this.cssBuild=e||"";this.ca=d||"";this.R=this.A=this.G=null}function X(a){return a?a.__styleInfo:null}function gh(a,b){return a.__styleInfo=b}fh.prototype.Jb=function(){return this.B};fh.prototype._getStyleRules=fh.prototype.Jb;function hh(a){var b=this.matches||this.matchesSelector||this.mozMatchesSelector||this.msMatchesSelector||this.oMatchesSelector||this.webkitMatchesSelector;return b&&b.call(this,a)}var ih=/:host\s*>\s*/,jh=navigator.userAgent.match("Trident");function kh(){}function lh(a){var b={},c=[],d=0;Cg(a,function(f){mh(f);f.index=d++;f=f.l.cssText;for(var g;g=wg.exec(f);){var h=g[1];g[2]!==":"&&(b[h]=!0)}},function(f){c.push(f)});a.Mb=c;a=[];for(var e in b)a.push(e);return a}
function mh(a){if(!a.l){var b={},c={};nh(a,c)&&(b.F=c,a.rules=null);b.cssText=a.parsedCssText.replace(zg,"").replace(ug,"");a.l=b}}function nh(a,b){var c=a.l;if(c){if(c.F)return Object.assign(b,c.F),!0}else{c=a.parsedCssText;for(var d;a=ug.exec(c);){d=(a[2]||a[3]).trim();if(d!=="inherit"||d!=="unset")b[a[1].trim()]=d;d=!0}return d}}
function oh(a,b,c){b&&(b=b.indexOf(";")>=0?ph(a,b,c):Jg(b,function(d,e,f,g){if(!e)return d+g;(e=oh(a,c[e],c))&&e!=="initial"?e==="apply-shim-inherit"&&(e="inherit"):e=oh(a,c[f]||f,c)||f;return d+(e||"")+g}));return b&&b.trim()||""}
function ph(a,b,c){b=b.split(";");for(var d=0,e,f;d<b.length;d++)if(e=b[d]){vg.lastIndex=0;if(f=vg.exec(e))e=oh(a,c[f[1]],c);else if(f=e.indexOf(":"),f!==-1){var g=e.substring(f);g=g.trim();g=oh(a,g,c)||g;e=e.substring(0,f)+g}b[d]=e&&e.lastIndexOf(";")===e.length-1?e.slice(0,-1):e||""}return b.join(";")}
function qh(a,b){var c={},d=[];Cg(a,function(e){e.l||mh(e);var f=e.o||e.parsedSelector;b&&e.l.F&&f&&hh.call(b,f)&&(nh(e,c),e=e.index,f=parseInt(e/32,10),d[f]=(d[f]||0)|1<<e%32)},null,!0);return{F:c,key:d}}
function rh(a,b,c,d){b.l||mh(b);if(b.l.F){var e=Mg(a);a=e.is;e=e.ca;e=a?W.ua(a,e):"html";var f=b.parsedSelector;var g=!!f.match(ih)||e==="html"&&f.indexOf("html")>-1;var h=f.indexOf(":host")===0&&!g;c==="shady"&&(g=f===e+" > *."+e||f.indexOf("html")!==-1,h=!g&&f.indexOf(e)===0);if(g||h)c=e,h&&(b.o||(b.o=W.cb(b,W.Ba,W.Ka(a),e)),c=b.o||e),g&&e==="html"&&(c=b.o||b.Mc),d({Ha:c,uc:h,Lc:g})}}
function sh(a,b,c){var d={},e={};Cg(b,function(f){rh(a,f,c,function(g){hh.call(a._element||a,g.Ha)&&(g.uc?nh(f,d):nh(f,e))})},null,!0);return{Bc:e,tc:d}}
function th(a,b,c,d){var e=Mg(b),f=W.ua(e.is,e.ca),g=new RegExp("(?:^|[^.#[:])"+(b.extends?"\\"+f.slice(0,-1)+"\\]":f)+"($|[.:[\\s>+~])"),h=X(b);e=h.B;h=h.cssBuild;var k=a.zb(b,e,d);return Vg(b,e,function(l){var n="";l.l||mh(l);l.l.cssText&&(n=ph(a,l.l.cssText,c));l.cssText=n;if(!T&&!Eg(l)&&l.cssText){var t=n=l.cssText;l.hb==null&&(l.hb=xg.test(n));if(l.hb)if(l.qa==null){l.qa=[];for(var D in k)t=k[D],t=t(n),n!==t&&(n=t,l.qa.push(D))}else{for(D=0;D<l.qa.length;++D)t=k[l.qa[D]],n=t(n);t=n}l.cssText=
t;a.cc(l,g,f,d)}},h)}kh.prototype.zb=function(a,b,c){a=b.Mb;b={};if(!T&&a)for(var d=0,e=a[d];d<a.length;e=a[++d])this.bc(e,c),b[e.keyframesName]=this.Nb(e);return b};kh.prototype.Nb=function(a){return function(b){return b.replace(a.wc,a.lb)}};kh.prototype.bc=function(a,b){a.wc=new RegExp("\\b"+a.keyframesName+"(?!\\B|-)","g");a.lb=a.keyframesName+"-"+b;a.o=a.o||a.selector;a.selector=a.o.replace(a.keyframesName,a.lb)};
kh.prototype.cc=function(a,b,c,d){a.o=a.o||a.selector;d="."+d;for(var e=Ng(a.o),f=0,g=e.length,h=void 0;f<g&&(h=e[f]);f++)e[f]=h.match(b)?h.replace(c,d):d+" "+h;a.selector=e.join(",")};function uh(a,b){var c=vh,d=Dg(a);a.textContent=Bg(d,function(e){var f=e.cssText=e.parsedCssText;e.l&&e.l.cssText&&(f=f.replace(mg,"").replace(ng,""),e.cssText=ph(c,f,b))})}ca.Object.defineProperties(kh.prototype,{nb:{configurable:!0,enumerable:!0,get:function(){return"x-scope"}}});var vh=new kh;var wh={},xh=window.customElements;if(xh&&!T&&!tg){var yh=xh.define;xh.define=function(a,b,c){wh[a]||(wh[a]=Hg(a));yh.call(xh,a,b,c)}};function zh(){this.cache={};this.Gc=100}zh.prototype.mc=function(a,b,c){for(var d=0;d<c.length;d++){var e=c[d];if(a.F[e]!==b[e])return!1}return!0};zh.prototype.store=function(a,b,c,d){var e=this.cache[a]||[];e.push({F:b,styleElement:c,A:d});e.length>this.Gc&&e.shift();this.cache[a]=e};zh.prototype.fetch=function(a,b,c){if(a=this.cache[a])for(var d=a.length-1;d>=0;d--){var e=a[d];if(this.mc(e,b,c))return e}};function Ah(){}var Bh=new RegExp(W.da+"\\s*([^\\s]*)");function Ch(a){return(a=(a.classList&&a.classList.value?a.classList.value:a.getAttribute("class")||"").match(Bh))?a[1]:""}function Dh(a){var b=Lg(a).getRootNode();return b===a||b===a.ownerDocument?"":(a=b.host)?Mg(a).is:""}
function Eh(a){for(var b=0;b<a.length;b++){var c=a[b];if(c.target!==document.documentElement&&c.target!==document.head)for(var d=0;d<c.addedNodes.length;d++){var e=c.addedNodes[d];if(e.nodeType===Node.ELEMENT_NODE){var f=e.getRootNode(),g=Ch(e);if(g&&f===e.ownerDocument&&(e.localName!=="style"&&e.localName!=="template"||Og(e)===""))Ug(e,g);else if(f instanceof ShadowRoot)for(f=Dh(e),f!==g&&Tg(e,g,f),e=window.ShadyDOM.nativeMethods.querySelectorAll.call(e,":not(."+W.da+")"),g=0;g<e.length;g++){f=e[g];
var h=Dh(f);h&&W.element(f,h)}}}}}
if(!(T||window.ShadyDOM&&window.ShadyDOM.handlesDynamicScoping)){var Fh=new MutationObserver(Eh),Gh=function(a){Fh.observe(a,{childList:!0,subtree:!0})};if(window.customElements&&!window.customElements.polyfillWrapFlushCallback)Gh(document);else{var Hh=function(){Gh(document.body)};window.HTMLImports?window.HTMLImports.whenReady(Hh):requestAnimationFrame(function(){if(document.readyState==="loading"){var a=function(){Hh();document.removeEventListener("readystatechange",a)};document.addEventListener("readystatechange",
a)}else Hh()})}Ah=function(){Eh(Fh.takeRecords())}};var Ih={};var Jh=Promise.resolve();function Kh(a){if(a=Ih[a])a._applyShimCurrentVersion=a._applyShimCurrentVersion||0,a._applyShimValidatingVersion=a._applyShimValidatingVersion||0,a._applyShimNextVersion=(a._applyShimNextVersion||0)+1}function Lh(a){return a._applyShimCurrentVersion===a._applyShimNextVersion}function Mh(a){a._applyShimValidatingVersion=a._applyShimNextVersion;a._validating||(a._validating=!0,Jh.then(function(){a._applyShimCurrentVersion=a._applyShimNextVersion;a._validating=!1}))};var Nh={},Oh=new zh;function Y(){this.Za={};this.L=document.documentElement;var a=new eg;a.rules=[];this.C=gh(this.L,new fh(a));this.wa=!1;this.h=this.m=null}m=Y.prototype;m.flush=function(){Ah()};m.Fb=function(a){var b=this.Za[a]=(this.Za[a]||0)+1;return a+"-"+b};m.rc=function(a){return Dg(a)};m.Fc=function(a){return Bg(a)};
m.Eb=function(a){var b=[];a=a.content.querySelectorAll("style");for(var c=0;c<a.length;c++){var d=a[c];if(d.hasAttribute("shady-unscoped")){if(!T){var e=d.textContent;if(!Ag.has(e)){Ag.add(e);var f=document.createElement("style");f.setAttribute("shady-unscoped","");f.textContent=e;document.head.appendChild(f)}d.parentNode.removeChild(d)}}else b.push(d.textContent),d.parentNode.removeChild(d)}return b.join("").trim()};
m.prepareTemplate=function(a,b,c){this.prepareTemplateDom(a,b);this.prepareTemplateStyles(a,b,c)};
m.prepareTemplateStyles=function(a,b,c){if(!a._prepared&&!tg){T||wh[b]||(wh[b]=Hg(b));a._prepared=!0;a.name=b;a.extends=c;Ih[b]=a;var d=Og(a),e=Pg(d);c={is:b,extends:c};var f=this.Eb(a)+(Nh[b]||"");this.V();if(!e){var g;if(g=!d)g=vg.test(f)||ug.test(f),vg.lastIndex=0,ug.lastIndex=0;var h=fg(f);g&&U&&this.m&&this.m.transformRules(h,b);a._styleAst=h}g=[];U||(g=lh(a._styleAst));if(!g.length||U)b=this.Gb(c,a._styleAst,T?a.content:null,wh[b]||null,d,e?f:""),a._style=b;a.Sb=g}};
m.prepareAdoptedCssText=function(a,b){Nh[b]=a.join(" ")};m.prepareTemplateDom=function(a,b){if(!tg){var c=Og(a);T||c==="shady"||a._domPrepared||(a._domPrepared=!0,Sg(a.content,b))}};m.Gb=function(a,b,c,d,e,f){f=Vg(a,b,null,e,f);return f.length?Fg(f,a.is,c,d):null};m.Wa=function(a){var b=Mg(a),c=b.is;b=b.ca;var d=wh[c]||null,e=Ih[c];if(e){c=e._styleAst;var f=e.Sb;e=Og(e);b=new fh(c,d,f,b,e);gh(a,b);return b}};
m.Ab=function(){return!this.m&&window.ShadyCSS&&window.ShadyCSS.ApplyShim?(this.m=window.ShadyCSS.ApplyShim,this.m.invalidCallback=Kh,!0):!1};m.Bb=function(){var a=this;!this.h&&window.ShadyCSS&&window.ShadyCSS.CustomStyleInterface&&(this.h=window.ShadyCSS.CustomStyleInterface,this.h.transformCallback=function(b){a.kb(b)},this.h.validateCallback=function(){requestAnimationFrame(function(){(a.h.enqueued||a.wa)&&a.flushCustomStyles()})})};m.V=function(){var a=this.Ab();this.Bb();return a};
m.flushCustomStyles=function(){if(!tg){var a=this.V();if(this.h){var b=this.h.processStyles();!a&&!this.h.enqueued||Pg(this.C.cssBuild)||(U?this.C.cssBuild||this.ac(b):(this.Wb(b),this.Ca(this.L,this.C),this.rb(b),this.wa&&this.styleDocument()),this.h.enqueued=!1)}}};
m.Wb=function(a){var b=this;a=a.map(function(c){return b.h.getStyleForCustomStyle(c)}).filter(function(c){return!!c});a.sort(function(c,d){c=d.compareDocumentPosition(c);return c&Node.DOCUMENT_POSITION_FOLLOWING?1:c&Node.DOCUMENT_POSITION_PRECEDING?-1:0});this.C.B.rules=a.map(function(c){return Dg(c)})};
m.styleElement=function(a,b){if(tg){if(b){X(a)||gh(a,new fh(null));var c=X(a);this.Ua(c,b);Ph(this,a,c)}}else if(c=X(a)||this.Wa(a))this.xa(a)||(this.wa=!0),b&&this.Ua(c,b),U?Ph(this,a,c):(this.flush(),this.Ca(a,c),c.Fa&&c.Fa.length&&this.sb(a,c))};m.Ua=function(a,b){a.T=a.T||{};Object.assign(a.T,b)};
function Ph(a,b,c){var d=Mg(b).is;if(c.T){var e=c.T,f;for(f in e)f===null?b.style.removeProperty(f):b.style.setProperty(f,e[f])}if(((e=Ih[d])||a.xa(b))&&(!e||Og(e)==="")&&e&&e._style&&!Lh(e)){if(Lh(e)||e._applyShimValidatingVersion!==e._applyShimNextVersion)a.V(),a.m&&a.m.transformRules(e._styleAst,d),e._style.textContent=Vg(b,c.B),Mh(e);T&&(a=b.shadowRoot)&&(a=a.querySelector("style"))&&(a.textContent=Vg(b,c.B));c.B=e._styleAst}}
m.Aa=function(a){return(a=Lg(a).getRootNode().host)?X(a)||this.Wa(a)?a:this.Aa(a):this.L};m.xa=function(a){return a===this.L};
m.sb=function(a,b){var c=Mg(a).is,d=Oh.fetch(c,b.G,b.Fa),e=d?d.styleElement:null,f=b.A;b.A=d&&d.A||this.Fb(c);var g=b.G;var h=b.A;var k=vh;g=e?e.textContent||"":th(k,a,g,h);k=X(a);var l=k.R;l&&!T&&l!==e&&(l._useCount--,l._useCount<=0&&l.parentNode&&l.parentNode.removeChild(l));T?k.R?(k.R.textContent=g,e=k.R):g&&(e=Fg(g,h,a.shadowRoot,k.placeholder)):e?e.parentNode||(jh&&g.indexOf("@media")>-1&&(e.textContent=g),Gg(e,null,k.placeholder)):g&&(e=Fg(g,h,null,k.placeholder));e&&(e._useCount=e._useCount||
0,k.R!=e&&e._useCount++,k.R=e);h=e;T||(e=b.A,k=g=a.getAttribute("class")||"",f&&(k=g.replace(new RegExp("\\s*x-scope\\s*"+f+"\\s*","g")," ")),k+=(k?" ":"")+"x-scope "+e,g!==k&&Kg(a,k));d||Oh.store(c,b.G,h,b.A);return h};
m.Ca=function(a,b){var c=this.Aa(a),d=X(c),e=d.G;c===this.L||e||(this.Ca(c,d),e=d.G);c=Object.create(e||null);e=sh(a,b.B,b.cssBuild);a=qh(d.B,a).F;Object.assign(c,e.tc,a,e.Bc);this.Qb(c,b.T);a=vh;d=Object.getOwnPropertyNames(c);e=0;for(var f;e<d.length;e++)f=d[e],c[f]=oh(a,c[f],c);b.G=c};m.Qb=function(a,b){for(var c in b){var d=b[c];if(d||d===0)a[c]=d}};m.styleDocument=function(a){this.styleSubtree(this.L,a)};
m.styleSubtree=function(a,b){var c=Lg(a),d=c.shadowRoot,e=this.xa(a);(d||e)&&this.styleElement(a,b);if(a=e?c:d)for(a=Array.from(a.querySelectorAll("*")).filter(function(f){return Lg(f).shadowRoot}),b=0;b<a.length;b++)this.styleSubtree(a[b])};m.ac=function(a){for(var b=0;b<a.length;b++){var c=this.h.getStyleForCustomStyle(a[b]);c&&this.Zb(c)}};m.rb=function(a){for(var b=0;b<a.length;b++){var c=this.h.getStyleForCustomStyle(a[b]);c&&uh(c,this.C.G)}};
m.kb=function(a){var b=this,c=Og(a);c!==this.C.cssBuild&&(this.C.cssBuild=c);if(!Pg(c)){var d=Dg(a);Cg(d,function(e){if(T)eh(e);else{var f=W;e.selector=e.parsedSelector;eh(e);f.bb(e,f.fc)}U&&c===""&&(b.V(),b.m&&b.m.transformRule(e))});U?a.textContent=Bg(d):this.C.B.rules.push(d)}};m.Zb=function(a){if(U&&this.m){var b=Dg(a);this.V();this.m.transformRules(b);a.textContent=Bg(b)}};
m.getComputedStyleValue=function(a,b){var c;U||(c=(X(a)||X(this.Aa(a))).G[b]);return(c=c||window.getComputedStyle(a).getPropertyValue(b))?c.trim():""};m.Ec=function(a,b){var c=Lg(a).getRootNode();b=b?(typeof b==="string"?b:String(b)).split(/\s/):[];c=c.host&&c.host.localName;if(!c){var d=a.getAttribute("class");if(d){d=d.split(/\s/);for(var e=0;e<d.length;e++)if(d[e]===W.da){c=d[e+1];break}}}c&&b.push(W.da,c);U||(c=X(a))&&c.A&&b.push(vh.nb,c.A);Kg(a,b.join(" "))};m.dc=function(a){return X(a)};
m.Dc=function(a,b){W.element(a,b)};m.Hc=function(a,b){W.element(a,b,!0)};m.Cc=function(a){return Dh(a)};m.qc=function(a){return Ch(a)};Y.prototype.flush=Y.prototype.flush;Y.prototype.prepareTemplate=Y.prototype.prepareTemplate;Y.prototype.styleElement=Y.prototype.styleElement;Y.prototype.styleDocument=Y.prototype.styleDocument;Y.prototype.styleSubtree=Y.prototype.styleSubtree;Y.prototype.getComputedStyleValue=Y.prototype.getComputedStyleValue;Y.prototype.setElementClass=Y.prototype.Ec;
Y.prototype._styleInfoForNode=Y.prototype.dc;Y.prototype.transformCustomStyleForDocument=Y.prototype.kb;Y.prototype.getStyleAst=Y.prototype.rc;Y.prototype.styleAstToString=Y.prototype.Fc;Y.prototype.flushCustomStyles=Y.prototype.flushCustomStyles;Y.prototype.scopeNode=Y.prototype.Dc;Y.prototype.unscopeNode=Y.prototype.Hc;Y.prototype.scopeForNode=Y.prototype.Cc;Y.prototype.currentScopeForNode=Y.prototype.qc;Y.prototype.prepareAdoptedCssText=Y.prototype.prepareAdoptedCssText;
Object.defineProperties(Y.prototype,{nativeShadow:{get:function(){return T}},nativeCss:{get:function(){return U}}});var Z=new Y,Qh,Rh;window.ShadyCSS&&(Qh=window.ShadyCSS.ApplyShim,Rh=window.ShadyCSS.CustomStyleInterface);
window.ShadyCSS={ScopingShim:Z,prepareTemplate:function(a,b,c){Z.flushCustomStyles();Z.prepareTemplate(a,b,c)},prepareTemplateDom:function(a,b){Z.prepareTemplateDom(a,b)},prepareTemplateStyles:function(a,b,c){Z.flushCustomStyles();Z.prepareTemplateStyles(a,b,c)},styleSubtree:function(a,b){Z.flushCustomStyles();Z.styleSubtree(a,b)},styleElement:function(a){Z.flushCustomStyles();Z.styleElement(a)},styleDocument:function(a){Z.flushCustomStyles();Z.styleDocument(a)},flushCustomStyles:function(){Z.flushCustomStyles()},
getComputedStyleValue:function(a,b){return Z.getComputedStyleValue(a,b)},nativeCss:U,nativeShadow:T,cssBuild:sg,disableRuntime:tg};Qh&&(window.ShadyCSS.ApplyShim=Qh);Rh&&(window.ShadyCSS.CustomStyleInterface=Rh);/*

Copyright (c) 2019 The Polymer Project Authors. All rights reserved.
This code may only be used under the BSD style license found at
http://polymer.github.io/LICENSE.txt The complete set of authors may be found at
http://polymer.github.io/AUTHORS.txt The complete set of contributors may be
found at http://polymer.github.io/CONTRIBUTORS.txt Code distributed by Google as
part of the polymer project is also subject to an additional IP rights grant
found at http://polymer.github.io/PATENTS.txt
*/
var Sh=window.customElements,Th=!1,Uh=null;Sh.polyfillWrapFlushCallback&&Sh.polyfillWrapFlushCallback(function(a){Uh=a;Th&&a()});function Vh(){Uh&&Uh();Th=!0;window.WebComponents.ready=!0;document.dispatchEvent(new CustomEvent("WebComponentsReady",{bubbles:!0}))}document.readyState!=="complete"?(window.addEventListener("load",Vh),window.addEventListener("DOMContentLoaded",function(){window.removeEventListener("load",Vh);Vh()})):Vh();})();
//# sourceMappingURL=webcomponents-all-noPatch.js.map
