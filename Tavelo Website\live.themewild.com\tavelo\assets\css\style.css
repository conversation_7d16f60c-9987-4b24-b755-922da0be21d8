/* =====================================================
Template Name   : Tavelo
Description     : Travel Booking HTML5 Template
Author          : LunarTemp
Version         : 1.0
=======================================================*/


/*=====================================================
Table of contents
=======================================================
1. Google fonts
2. Theme variables
3. General css
4. Theme default css
5. Site title css
6. Margin & padding
7. Scroll-top css
8. Header top css
9. Nav menu css
10. Multi level dropdown menu
11. Search css
12. Date picker
13. Time picker css 
14. Price range slider
15. Play btn
16. Destination css 
17. Flight css 
18. Hotel css 
19. Room type css 
20. Room css 
21. Banner css 
22. Tour css 
23. Car css 
24. Activity css 
25. Cruise css 
26. Listing single css 
27. Deal css 
28. Download css 
29. Choose css 
30. Booking sidebar
31. Booking sort
32. Flight booking css 
33. Cart css 
34. Booking css 
35. Booking confirm css 
36. About css 
37. Service css 
38. Service single
39. Feature css 
40. Video css 
41. Video css 
42. Testimonial css 
43. Counter css 
44. Cta-area css 
45. Gallery css 
46. Mission css 
47. Career css 
48. Career single css 
49. Blog css 
50. Blog single css
51. Partner css
52. Contact us css 
53. Team css 
54. Pricing css 
55. Faq css 
56. Breadcrumb css
57. Pagination css 
58. Auth css 
59. User profile css 
60. Coming soon css 
61. Error css 
62. Terms/privacy css 
63. Footer css 
64. Home 2
65. Home 3
66. Home 4
=======================================================*/


/* ===================
1. Google fonts
====================== */

@import url('https://fonts.googleapis.com/css2?family=Barlow:wght@300;400;500;600;700;800;900&amp;family=Roboto:wght@100;300;400;500;700;900&amp;display=swap');



/*====================
2. Theme variables
======================*/

:root {
  --body-font: 'Roboto', sans-serif;
  --heading-font: 'Barlow', sans-serif;
  --theme-color: #24BDC7;
  --theme-color2: #F96768;
  --theme-color-light: rgba(37, 189, 199, .12);
  --theme-bg-light: rgba(37, 189, 199, .08);
  --body-text-color: #757F95;
  --color-white: #FFFFFF;
  --color-dark: #0C2C7A;
  --color-green: #15D4C9;
  --color-blue: #0049D0;
  --color-skyblue: #00BFFF;
  --color-yellow: #FBA707;
  --color-gray: #ECECEC;
  --color-red: #F05454;
  --hero-overlay-color: #01060F;
  --slider-arrow-bg: rgba(255, 255, 255, 0.2);
  --box-shadow: 0 0 40px 5px rgb(0 0 0 / 5%);
  --box-shadow2: 0 0 15px rgba(0, 0, 0, 0.17);
  --transition: all .5s ease-in-out;
  --transition2: all .3s ease-in-out;
  --border-info-color: rgba(0, 0, 0, 0.08);
  --border-info-color2: rgba(0, 0, 0, 0.05);
  --border-white-color: rgba(255, 255, 255, 0.12);
  --border-white-color2: rgba(255, 255, 255, 0.07);
  --footer-bg: #0C2C7A;
  --footer-text-color: #F5FAFF;
}




/* ===================
3. General css
====================== */

*,
*:before,
*:after {
  box-sizing: inherit;
}

* {
  scroll-behavior: inherit !important;
}

html,
body {
  height: auto;
  width: 100%;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  box-sizing: border-box;
}

body {
  font-family: 'Roboto', sans-serif;
  font-style: normal;
  font-size: 16px;
  font-weight: normal;
  color: var(--body-text-color);
  line-height: 1.8;
}

a {
  color: var(--color-dark);
  display: inline-block;
}

a,
a:active,
a:focus,
a:hover {
  outline: none;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  text-decoration: none;
}

a:hover {
  color: #0049D0;
}

ul {
  margin: 0;
  padding: 0;
}

li {
  list-style: none;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: var(--color-dark);
  margin: 0px;
  font-weight: 600;
  font-family: var(--heading-font);
  line-height: 1.2;
}

h1 {
  font-size: 40px;
}

h2 {
  font-size: 35px;
}

h3 {
  font-size: 28px;
}

h4 {
  font-size: 22px;
}

h5 {
  font-size: 18px;
}

h6 {
  font-size: 16px;
}

p {
  margin: 0px;
}

.img,
img {
  max-width: 100%;
  -webkit-transition: all 0.3s ease-out 0s;
  -moz-transition: all 0.3s ease-out 0s;
  -ms-transition: all 0.3s ease-out 0s;
  -o-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
  height: auto;
}

label {
  color: #999;
  cursor: pointer;
  font-weight: 400;
}

*::-moz-selection {
  background: #d6b161;
  color: var(--color-white);
  text-shadow: none;
}

::-moz-selection {
  background: #555;
  color: var(--color-white);
  text-shadow: none;
}

::selection {
  background: #555;
  color: var(--color-white);
  text-shadow: none;
}

*::-moz-placeholder {
  color: #999;
  font-size: 16px;
  opacity: 1;
}

*::placeholder {
  color: #999;
  font-size: 16px;
  opacity: 1;
}

/* ===================
preloader
====================== */

.preloader {
  position: fixed;
  width: 100%;
  height: 100%;
  background: var(--theme-color);
  display: flex;
  align-items: center;
  justify-content: center;
  top: 0;
  left: 0;
  z-index: 99999;
}

.loader {
  position: relative;
  width: 90px;
  height: 90px;
}

.loader span {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transform: rotate(calc(18deg * var(--i)))
}

.loader span::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 10px;
  height: 10px;
  background: var(--color-white);
  border-radius: 50%;
  transform: scale(0);
  animation: loader 2s linear infinite;
  animation-delay: calc(0.1s * var(--i));
}

@keyframes loader {
  0% {
    transform: scale(0);
  }

  10% {
    transform: scale(1.2);
  }

  80%,
  100% {
    transform: scale(0);
  }
}

.loader-plane {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  animation: loader-rotating 2s linear infinite;
  animation-delay: -1s;
}

@keyframes loader-rotating {
  0% {
    transform: rotate(10deg);
  }

  100% {
    transform: rotate(370deg);
  }
}

.loader-plane::before {
  content: '\f072';
  position: absolute;
  font-family: "Font Awesome 6 Pro";
  font-weight: 300;
  top: 53px;
  left: 58px;
  color: var(--color-white);
  font-size: 38px;
  transform: rotate(135deg);
}




/* ===================
4. Theme default css
====================== */

.ovrflow-hidden {
  overflow: hidden;
}

.text-right {
  text-align: right;
}

.space-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.c-pd {
  padding: 0 7rem;
}

.s-pd {
  padding: 0 12rem;
}

.h-100 {
  height: 100%;
}

.h-100vh {
  height: 100vh;
}

.bg {
  background-color: var(--theme-bg-light);
}

@media all and (max-width: 1399px) {
  .container{
    max-width: 95%;
  }
}



/* ===================
5. Site title css
====================== */

.site-heading {
  margin-bottom: 50px;
  position: relative;
}

.site-heading-inline{
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 15px;
}

.site-title-tagline {
  text-transform: uppercase;
  letter-spacing: 3px;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-white);
  position: relative;
  background: var(--theme-color);
  border-radius: 50px;
  padding: 5px 10px 5px 2px;
}

.site-title-tagline i {
  font-size: 16px;
  width: 30px;
  height: 30px;
  line-height: 30px;
  background: var(--color-white);
  color: var(--theme-color);
  text-align: center;
  border-radius: 50px;
}

.site-title {
  font-weight: 700;
  text-transform: capitalize;
  font-size: 40px;
  color: var(--color-dark);
  margin-top: 8px;
  margin-bottom: 0;
}

.site-title span {
  color: var(--theme-color);
}

.site-heading p {
  margin-top: 15px;
}

.heading-divider {
  display: inline-block;
  position: relative;
  height: 3px;
  -webkit-border-radius: 30px;
  -moz-border-radius: 30px;
  border-radius: 30px;
  background-color: var(--theme-color);
  width: 90px;
  overflow: hidden;
}

.heading-divider:after {
  content: '';
  position: absolute;
  left: 0;
  top: -1.1px;
  height: 7px;
  width: 8px;
  background-color: var(--color-white);
  -webkit-animation: heading-move 5s infinite linear;
  animation: heading-move 5s infinite linear;
}

@-webkit-keyframes heading-move {
  0% {
    transform: translateX(-1px);
  }

  50% {
    transform: translateX(85px);
  }

  100% {
    transform: translateX(-1px);
  }
}

@keyframes heading-move {
  0% {
    transform: translateX(-1px);
  }

  50% {
    transform: translateX(85px);
  }

  100% {
    transform: translateX(-1px);
  }
}


@media all and (max-width: 992px) {
  .shadow-title {
    font-size: 100px;
  }
}



/* ===================
6. Margin & padding
====================== */

.pb-140 {
  padding-bottom: 140px;
}

.pd-50 {
  padding: 50px 0;
}

.py-120 {
  padding: 120px 0;
}

.pt-120 {
  padding-top: 120px;
}

.pb-120 {
  padding-bottom: 120px;
}

.pt-0 {
  padding-top: 0px;
}

.pt-10 {
  padding-top: 10px;
}

.pt-20 {
  padding-top: 20px;
}

.pt-30 {
  padding-top: 30px;
}

.pt-40 {
  padding-top: 40px;
}

.pt-50 {
  padding-top: 50px;
}

.pt-60 {
  padding-top: 60px;
}

.pt-70 {
  padding-top: 70px;
}

.pt-80 {
  padding-top: 80px;
}

.pt-90 {
  padding-top: 90px;
}

.pt-100 {
  padding-top: 100px;
}

.pb-0 {
  padding-bottom: 0px;
}

.pb-10 {
  padding-bottom: 10px;
}

.pb-20 {
  padding-bottom: 20px;
}

.pb-30 {
  padding-bottom: 30px;
}

.pb-40 {
  padding-bottom: 40px;
}

.pb-50 {
  padding-bottom: 50px;
}

.pb-60 {
  padding-bottom: 60px;
}

.pb-70 {
  padding-bottom: 70px;
}

.pb-80 {
  padding-bottom: 80px;
}

.pb-90 {
  padding-bottom: 90px;
}

.pb-100 {
  padding-bottom: 100px;
}

.mt-0 {
  margin-top: 0px;
}

.mt-10 {
  margin-top: 10px;
}

.mt-20 {
  margin-top: 20px;
}

.mt-30 {
  margin-top: 30px;
}

.mt-40 {
  margin-top: 40px;
}

.mt-50 {
  margin-top: 50px;
}

.mt-60 {
  margin-top: 60px;
}

.mt-70 {
  margin-top: 70px;
}

.mt-80 {
  margin-top: 80px;
}

.mt-90 {
  margin-top: 90px;
}

.mt-100 {
  margin-top: 100px;
}

.mb-0 {
  margin-bottom: 0px;
}

.mb-10 {
  margin-bottom: 10px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mb-30 {
  margin-bottom: 30px;
}

.mb-40 {
  margin-bottom: 40px;
}

.mb-50 {
  margin-bottom: 50px;
}

.mb-60 {
  margin-bottom: 60px;
}

.mb-70 {
  margin-bottom: 70px;
}

.mb-80 {
  margin-bottom: 80px;
}

.mb-90 {
  margin-bottom: 90px;
}

.mb-100 {
  margin-bottom: 100px;
}

.mr-300 {
  margin-right: 300px;
}

.ml-300 {
  margin-left: 300px;
}

.pos-rel {
  position: relative;
  z-index: 1;
}

/* ===================
theme button
====================== */

.theme-btn {
  font-size: 16px;
  color: var(--color-white);
  padding: 10px 18px;
  transition: all 0.5s;
  text-transform: capitalize;
  position: relative;
  border-radius: 18px;
  font-weight: 500;
  cursor: pointer;
  text-align: center;
  overflow: hidden;
  border: none;
  background: var(--theme-color);
  box-shadow: 0 3px 24px rgb(0 0 0 / 12%);
  z-index: 1;
}

.theme-btn::before {
  content: "";
  height: 400px;
  width: 400px;
  background: var(--theme-color2);
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateY(-50%) translateX(-50%) scale(0);
  transition: 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  z-index: -1;
}

.theme-btn:hover {
  color: var(--color-white);
}

.theme-btn:hover::before {
  transform: translateY(-50%) translateX(-50%) scale(1);
}

.theme-btn i {
  margin-left: 5px;
}

.theme-btn span {
  margin-right: 5px;
}

.theme-btn2 {
  background: var(--color-white);
  color: var(--color-dark);
}

.theme-btn2::before {
  background: var(--theme-color);
}

.theme-btn2:hover {
  color: var(--color-white);
}




/* ===================
7. Scroll-top css
====================== */

#scroll-top {
  display: none;
  position: fixed;
  bottom: 20px;
  right: 30px;
  z-index: 99;
  font-size: 25px;
  border: none;
  outline: none;
  border-radius: 50px 50px 50px 0;
  color: var(--color-white);
  background: var(--theme-color);
  cursor: pointer;
  width: 45px;
  height: 45px;
  line-height: 45px;
  text-align: center;
  transition: var(--transition);
  z-index: 1;
}


@media all and (min-width: 768px) and (max-width: 1399px) {
  #scroll-top {
    bottom: 70px;
  }
}




/* ===================
8. Header top css
====================== */

.header-top {
  background: transparent;
  position: relative;
  border-bottom: 1px solid var(--border-white-color);
  padding: 6px 0;
  z-index: 9999;
}

.header-top-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.top-contact-info ul {
  display: flex;
  align-items: center;
  gap: 20px;
}

.top-contact-info ul li a {
  color: var(--color-white);
  font-weight: 500;
  transition: var(--transition);
}

.top-contact-info ul li a:hover {
  color: var(--theme-color);
}

.top-contact-info ul li a i {
  margin-right: 10px;
}

.top-social a {
  color: var(--color-white);
  font-size: 15px;
  width: 33px;
  height: 33px;
  line-height: 29.5px;
  border-radius: 50px;
  text-align: center;
  border: 2px solid var(--color-white);
  margin-right: 10px;
  transition: var(--transition);
}

.top-social a:hover {
  color: var(--theme-color);
  background: var(--color-white);
}

.header-top-right {
  display: flex;
  align-items: center;
  justify-content: end;
}

.header-top-right .nice-select {
  background: transparent;
  border: none;
  color: var(--color-white);
  font-weight: 500;
  line-height: 43px;
}

.header-top-right .nice-select:after {
  border-color: var(--color-white);
  height: 7px;
  width: 7px;
  margin-top: -5.5px;
}

.header-top-right .nice-select .list {
  border-radius: 15px;
  box-shadow: 0 3px 24px rgb(0 0 0 / 10%);
}

.header-top-right .nice-select .option {
  color: #000;
  transition: var(--transition);
}

.header-top-right .nice-select .option.focus,
.header-top-right .nice-select .option.selected.focus,
.header-top-right .nice-select .option:hover {
  background: #F6F6F6;
  color: var(--theme-color);
}

.header-top-right .account {
  margin-left: 20px;
  display: flex;
  align-items: center;
  gap: 20px;
}

.header-top-right .account a {
  color: var(--color-white);
  font-weight: 500;
  transition: var(--transition);
}

.header-top-right .account a:hover {
  color: var(--theme-color);
}

.header-top-right .account a i {
  margin-right: 10px;
}

@media all and (max-width: 1199px) {
  .top-social {
    display: none;
  }
}

@media all and (max-width: 991px) {
  .header-top {
    display: none;
  }
}




/* ===================
9. Nav menu css
====================== */

.main {
  margin-top: -10rem;
}

@media all and (min-width: 992px) {
  .navbar .nav-item .nav-link {
    margin-right: 22px;
    padding: 30px 0 30px 0;
    font-size: 17px;
    font-weight: 500;
    color: var(--color-white);
    text-transform: capitalize;
  }

  .navbar .nav-item:last-child .nav-link {
    margin-right: 0;
  }

  .navbar .nav-item .dropdown-menu {
    display: block;
    opacity: 0;
    visibility: hidden;
    transition: .3s;
    margin-top: 0;
    padding: 8px;
    border: none;
    left: -15px;
    border-radius: 10px;
    background: var(--color-white);
    width: 220px;
    box-shadow: 0 0 50px 0 rgb(32 32 32 / 15%);
  }

  .navbar .nav-item .dropdown-menu::before {
    content: "\f0d8";
    position: absolute;
    font-family: "Font Awesome 6 Pro";
    color: var(--color-white);
    font-size: 45px;
    font-weight: bold;
    left: 25px;
    top: -40px;
  }

  .navbar .nav-item .dropdown-menu li {
    border-bottom: 1px dashed var(--border-info-color);
  }

  .navbar .nav-item .dropdown-menu li:last-child {
    margin-bottom: 0;
    border-bottom: none;
  }

  .navbar .nav-item .dropdown-menu .dropdown-item {
    font-size: 16px;
    font-weight: 500;
    padding: 7px 0px 7px 20px;
    color: var(--color-dark);
    position: relative;
    text-transform: capitalize;
    transition: var(--transition);
    z-index: 1;
  }

  .navbar .nav-item .dropdown-menu .dropdown-item:hover {
    background: transparent;
    color: var(--theme-color);
    padding-left: 30px;
  }

  .navbar .nav-item .dropdown-menu .dropdown-item::before {
    content: "\f111";
    position: absolute;
    font-family: "Font Awesome 6 pro";
    left: 15px;
    top: 16px;
    color: var(--theme-color);
    font-weight: bold;
    font-size: 6px;
    transform: scale(0, 0);
    transition: var(--transition);
    z-index: -1;
  }

  .navbar .nav-item .dropdown-menu .dropdown-item:hover::before {
    transform: scale(1, 1);
  }

  .navbar .nav-item .nav-link {
    position: relative;
  }

  .navbar .nav-item .nav-link.active,
  .navbar .nav-item:hover .nav-link {
    color: var(--theme-color);
  }

  .navbar .nav-item:hover .dropdown-menu {
    transition: .3s;
    opacity: 1;
    visibility: visible;
    top: 100%;
    transform: rotateX(0deg);
  }

  .navbar .dropdown-menu-end {
    right: 0;
    left: auto;
  }

  .navbar .dropdown-menu.fade-down {
    top: 80%;
    transform: rotateX(-75deg);
    transform-origin: 0% 0%;
  }

  .navbar .dropdown-menu.fade-up {
    top: 140%;
  }

  .header-nav-right {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    margin-left: 30px;
  }

  .header-nav-link {
    position: relative;
    width: 45px;
    height: 45px;
    line-height: 45px;
    color: var(--color-dark);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .header-nav-link:hover {
    color: var(--theme-color);
  }

  .header-nav-link.search-box-outer {
    font-size: 18px;
  }

  #main_nav {
    justify-content: flex-end;
  }


  /* mega menu */
  .navbar .dropdown-menu.mega-menu{
    width: 850px;
    left: -345px;
    padding: 15px 10px;
  }

  .navbar .dropdown-menu.mega-menu::before{
    display: none;
  }

  .navbar .dropdown-menu.mega-menu div[class*='col-']{
    border-right: 1px solid rgba(0, 0, 0, .05);
  }

  .navbar .dropdown-menu.mega-menu .dropdown-item{
    padding-top: 5px;
    padding-bottom: 5px;
  }

  .navbar .dropdown-menu.mega-menu .dropdown-item::before{
    top: 15px;
  }

  .navbar .dropdown-menu.mega-menu ul li{
    border-bottom: none;
  }

}

/*bootstrap nav*/
.navbar {
  background: transparent;
  padding-top: 0px;
  padding-bottom: 0px;
  z-index: 999;
}

.navbar.fixed-top {
  background: var(--color-white);
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.17);
  animation: slide-down 0.7s;
}

.navbar.fixed-top .nav-item .nav-link {
  color: var(--color-dark);
}

.navbar.fixed-top .header-nav-link {
  color: var(--color-dark);
}

.navbar.fixed-top .header-nav-link:hover {
  color: var(--theme-color);
}

.navbar.fixed-top .nav-item .nav-link.active,
.navbar.fixed-top .nav-item:hover .nav-link {
  color: var(--theme-color);
}

@keyframes slide-down {
  0% {
    transform: translateY(-100%);
  }

  100% {
    transform: translateY(0);
  }
}

.navbar .navbar-brand .logo-display {
  display: block;
}

.navbar .navbar-brand .logo-scrolled {
  display: none;
}

.navbar.fixed-top .navbar-brand .logo-display {
  display: none;
}

.navbar.fixed-top .navbar-brand .logo-scrolled {
  display: block;
}

button.navbar-toggler:focus {
  outline: none;
  box-shadow: none;
}

.navbar-toggler-btn-icon {
  display: inline-block;
  width: inherit;
  height: inherit;
}

.navbar-brand {
  margin-right: 0;
}

.navbar-brand img {
  width: 190px;
}

/* dropdown toggler */
.navbar .dropdown-toggle::after {
  display: inline-block;
  margin-left: 5px;
  vertical-align: baseline;
  font-family: 'Font Awesome 6 Pro';
  content: "\f107";
  font-weight: 600;
  border: none;
  font-size: 14px;
}


@media all and (max-width: 1199px) {
  .header-nav-right {
    gap: 5px;
    margin-left: 15px;
  }

  .navbar .nav-item .nav-link {
    margin-right: 15px;
  }

  .navbar .header-btn {
    display: none;
  }
}


/* mobile menu */
.mobile-menu-right {
  display: none;
}

@media all and (max-width: 991px) {
  .navbar {
    top: 0;
    right: 0;
    left: 0;
    position: fixed;
  }

  .navbar-bg {
    height: 59px;
  }

  .navbar-bg::before {
    transform: translateY(0px) skew(-15deg);
    left: -8px;
  }

  .navbar-brand img {
    width: 130px;
  }

  .navbar-collapse {
    max-height: 220px;
    overflow: hidden;
    overflow-y: auto;
    padding: 0 20px;
    background-color: var(--color-white);
    border-radius: 15px;
  }

  .dropdown-toggle::after {
    float: right;
  }

  .navbar .nav-item .nav-link {
    color: var(--color-dark);
    font-weight: 700;
    transition: var(--transition);
    margin-right: 0;
  }

  .navbar .nav-item .nav-link:hover {
    color: var(--theme-color) !important;
  }

  .navbar-toggler {
    padding: 0;
    border: none;
  }

  .mobile-menu-right {
    display: flex;
    align-items: center;
  }

  .mobile-menu-link {
    font-size: 20px;
    color: var(--color-dark);
    font-weight: 500;
    padding-right: 20px;
    margin-bottom: 0px;
    position: relative;
  }

  .mobile-menu-link:hover {
    color: var(--theme-color);
  }

  .navbar-toggler-btn-icon {
    font-size: 25px;
    color: var(--color-white);
    font-weight: 500;
  }

  .navbar.fixed-top .mobile-menu-link {
    color: var(--color-dark);
  }

  .navbar.fixed-top .navbar-toggler-btn-icon {
    color: var(--color-dark);
  }

  .navbar .dropdown-menu {
    border-radius: 15px;
  }

  .navbar .dropdown-menu .dropdown-item{
    color: var(--color-dark);
  }

  .header-nav-right {
    display: none;
  }

}



/* ======================
10. Multi level dropdown menu
========================= */

.navbar .nav-item .dropdown-submenu {
  position: relative;
}

.navbar .nav-item .dropdown-submenu .dropdown-menu::before {
  display: none;
}

.navbar .nav-item .dropdown-submenu a::after {
  transform: rotate(-90deg);
  position: absolute;
  right: 15px;
  top: 9px;
  font-weight: 600;
}

.navbar .nav-item .dropdown-submenu a:hover {
  background: transparent;
  color: var(--color-white);
}

.navbar .nav-item .dropdown-submenu .dropdown-menu {
  top: 120%;
  left: 100%;
  opacity: 0;
  visibility: hidden;
}

.navbar .nav-item .dropdown-submenu:hover .dropdown-menu {
  top: 0;
  opacity: 1;
  visibility: visible;
}

@media all and (max-width: 991px) {
  .navbar .nav-item .dropdown-submenu .dropdown-menu {
    margin: 0 17px;
  }

  .navbar .nav-item .dropdown-submenu .dropdown-menu {
    opacity: unset;
    visibility: unset;
  }

  .navbar .nav-item .dropdown-submenu a::after {
    top: 4px;
  }

  .navbar .nav-item .dropdown-submenu a:hover {
    color: var(--theme-color);
  }
}




/* ===================
hero css 
====================== */

.hero-section {
  position: relative;
}

.hero-single {
  padding-top: 300px;
  padding-bottom: 280px;
  background-position: center !important;
  background-size: cover !important;
  background-repeat: no-repeat !important;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 1;
}

.hero-single::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  background: rgba(0, 0, 0, .65);
  z-index: -1;
}

.hero-single .hero-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
}

.hero-single .hero-content .hero-title {
  color: var(--color-white);
  font-size: 75px;
  font-weight: 600;
  margin: 20px 0;
  text-transform: capitalize;
}

.hero-single .hero-content .hero-title span {
  color: var(--theme-color);
}

.hero-single .hero-content p {
  color: var(--color-white);
  font-size: 22px;
  line-height: 30px;
  font-weight: 400;
  margin-bottom: 20px;
  text-transform: capitalize;
}

.hero-single .hero-content .hero-btn {
  gap: 2rem;
  display: flex;
  margin-top: 1rem;
  justify-content: start;
}

.hero-slider .hero-single::before{
  left: -0.5px;
}

.hero-slider.owl-theme .owl-nav {
  margin-top: 0px;
}

.hero-slider.owl-theme .owl-nav [class*=owl-] {
  color: var(--theme-color);
  font-size: 22px;
  margin: 0;
  padding: 0;
  background: var(--color-white);
  display: inline-block;
  cursor: pointer;
  height: 52px;
  width: 52px;
  line-height: 52px;
  border-radius: 52px;
  text-align: center;
  transition: var(--transition);
}

.hero-slider.owl-theme .owl-nav [class*=owl-]:hover {
  background: var(--theme-color);
  color: var(--color-white);
}

.hero-slider.owl-theme .owl-nav .owl-prev {
  left: 40px;
}

.hero-slider.owl-theme .owl-nav .owl-next {
  right: 40px;
}

.hero-slider.owl-theme .owl-nav .owl-prev,
.hero-slider.owl-theme .owl-nav .owl-next {
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
}

@media all and (max-width: 1199px) {

  .hero-slider.owl-theme .owl-nav .owl-prev,
  .hero-slider.owl-theme .owl-nav .owl-next {
    top: unset;
    bottom: 125px !important;
  }

  .hero-slider.owl-theme .owl-nav .owl-prev {
    left: unset;
    right: 120px;
  }

  .hero-slider.owl-theme .owl-nav .owl-next {
    right: 40px;
  }

}

@media all and (max-width: 991px) {
  .hero-single .hero-content .hero-btn {
    gap: 1.5rem;
  }

  .hero-single .hero-content .hero-btn .theme-btn {
    padding: 12px 15px
  }
}

@media all and (max-width: 767px) {
  .hero-single .hero-content .hero-title {
    font-size: 40px;
  }

  .hero-single .hero-content .hero-sub-title {
    font-size: 18px;
  }

  .hero-single .hero-content .theme-btn {
    padding-left: 20px;
  }
}




/* =====================
11. Search css
===================== */

.search-area {
  position: relative;
  z-index: 2;
}

.search-wrapper {
  position: relative;
  margin-top: -150px;
}

.search-header{
  display: flex;
  align-items: start;
  justify-content: start;
  flex-direction: column;
  margin-left: 50px;
}

.search-nav{
  display: inline-block;
  background: var(--color-white);
  border-radius: 50px;
  margin-bottom: -28px;
}

.search-nav .nav-item {
  margin: 5px;
}

.search-nav .nav-link {
  background: var(--theme-color-light);
  border-radius: 50px;
  padding: 6px 16px;
  font-weight: 500;
  color: var(--color-dark);
  position: relative;
  transition: none;
}

.search-nav .nav-link i{
  margin-right: 8px;
  color: var(--theme-color);
}

.search-nav .nav-link.active i{
  color: var(--color-white);
}

.search-nav .nav-link.active {
  background: var(--theme-color);
}

.search-wrapper .tab-content{
  background: var(--color-white);
  padding: 45px 25px 25px 25px;
  border-radius: 50px;
  box-shadow: var(--box-shadow);
}

.search-wrapper .search-box{
  background: var(--color-white);
  padding: 30px 30px 50px 30px;
  border-radius: 20px;
  box-shadow: var(--box-shadow);
}

.search-form .form-group {
  position: relative;
  padding: 20px 25px;
  border-radius: 30px;
  background: var(--theme-color-light);
}

.search-form .form-group label,
.search-form .form-check label{
  color: var(--color-dark);
}

.search-form .form-control {
  border-radius: 12px;
  padding: 5px 0px;
  box-shadow: none;
  color: var(--color-dark);
  background: transparent;
  border: none;
  font-size: 19px;
  font-weight: 500;
}

.search-form .form-group i {
  position: absolute;
  right: 20px;
  top: 10px;
  font-size: 28px;
  color: var(--theme-color);
}

.search-form .form-group p {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  color: var(--color-dark);
  font-size: 15px;
}

.search-form-swap{
  position: absolute;
  left: -33px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
}

.search-form-swap i{
  background: var(--theme-color);
  color: var(--color-white) !important;
  font-size: 19px !important;
  position: unset !important;
  width: 40px;
  height: 40px;
  text-align: center;
  line-height: 35px;
  border-radius: 50%;
  border: 3px solid var(--color-white);
}

.search-form-date{
  display: flex;
}

.search-form .passenger-total{
  border-radius: 12px;
  padding: 2.4px 0px;
  color: var(--color-dark);
  font-size: 19px;
  font-weight: 500;
}

.search-form .passenger-box{
  cursor: pointer;
}

.search-form .passenger-box .dropdown-menu{
  background: var(--color-white);
  border: none;
  border-radius: 25px;
  padding: 15px 10px;
  width: 291px;
  right: -15px !important;
  margin-top: 15px !important;
  box-shadow: var(--box-shadow);
}

.search-form .static-dropdown.passenger-box .dropdown-menu{
  right: 0px !important;
} 

.search-form .passenger-box .dropdown-item:hover{
  background: transparent;
}

.search-form .passenger-item{
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 5px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(0, 0, 0, .05);
}

.search-form .passenger-qty{
  display: flex;
  align-items: center;
  gap: 5px;
}

.search-form .passenger-qty button{
  border: none;
  width: 35px;
  height: 35px;
  line-height: 32px;
  border-radius: 50px;
  background: var(--theme-color-light);
}

.search-form .passenger-qty button i{
  position: unset !important;
  font-size: 16px !important;
}

.search-form .passenger-qty input{
  width: 25px;
  text-align: center;
  border: none;
  outline: none;
  font-weight: 500;
  color: var(--color-dark);
}

.search-form .passenger-class-info .form-check{
  margin-top: 10px;
}

.search-form .passenger-class-info .form-check-input{
  box-shadow: none;
  margin-top: 7px;
}

.search-form .search-btn .theme-btn{
  position: absolute;
  border-radius: 50px;
  border: 5px solid var(--color-white);
  top: 0px;
  right: 50px;
  padding: 7px 18px;
  white-space: nowrap;
  box-shadow: none;
}

/* search-common */
.search-common .search-wrapper{
  margin-top: -90px;
}

.search-common .search-wrapper .search-box{
  border-radius: 50px;
  padding: 25px;
}

.search-common .search-form .search-btn .theme-btn{
  right: unset;
  top: unset;
  left: 50%;
  transform: translateX(-50%);
  bottom: -33px;
}

/* flight */
.flight-search .search-wrapper{
  margin-top: -100px;
}

.flight-search .search-wrapper .search-box{
  padding: 15px;
  border-radius: 30px;
}

.flight-search .flight-type{
  margin-bottom: 15px;
}

.flight-search .search-form .search-btn .theme-btn{
  right: 0;
  transform: unset;
}

.flight-search .search-form .flight-type .form-check-input{
  margin-top: 6.5px;
  box-shadow: none;
  border: 2px solid var(--color-dark);
}

.flight-search .search-form .flight-type .form-check-label{
  font-weight: 500;
  color: var(--color-dark);
}

.flight-search .search-form .form-group{
  border-radius: 20px;
}

.flight-search .search-wrapper .flight-search-wrapper .tab-content{
  box-shadow: none;
  background: none;
  border-radius: 0;
  padding: 0;
}

.flight-search .flight-search .search-form-return{
  display: none;
}

.flight-search .search-form .flight-multicity-item{
  margin-top: 25px;
  display: none;
}

.flight-search .search-form .multicity-btn{
  padding-top: 31px;
  padding-bottom: 31px;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
  cursor: pointer;
  color: var(--color-dark);
}

.flight-search .search-form .multicity-item-remove{
  color: var(--theme-color2);
}

.flight-search .search-form .multicity-item-remove i{
  color: var(--theme-color2) !important;
}

.flight-search.ft-group .search-form .form-group{
  border-radius: 30px;
}

.flight-search.ft-group .search-form .search-btn .theme-btn{
  right: 50px;
}

/* holiday */
.holiday-search .flight-type{
  margin-bottom: 15px;
}

.holiday-search .flight-type .form-check label{
  font-weight: 500;
}

.holiday-search .flight-type .form-check-input{
  box-shadow: none;
  margin-top: 6px;
  border-color: var(--color-dark);
  border-width: 2px;
}


@media all and (max-width: 1199px) {
  .search-area .container{
    max-width: 100%;
  }

  .search-form .form-group{
    margin-bottom: 20px;
  }

  .search-form .search-btn .theme-btn{
    top: unset;
    right: unset;
    left: 50%;
    bottom: -20px;
    transform: translateX(-50%);
  }
} 

@media all and (max-width: 991px) {
  .search-header{
    margin-left: 0;
  }

  .search-nav{
    border-radius: 20px 20px 0 0;
  }

  .search-nav .nav{
    justify-content: center;
  }

  .search-wrapper .tab-content{
    border-radius: 20px;
  }

  .flight-search .search-form-swap{
    left: 50%;
    top: -30px;
    transform: translateX(-50%);
  }
} 



/* =====================
12. Date picker
===================== */

.ui-widget.ui-widget-content{
  border: none;
  padding: 8px;
  border-radius: 20px;
  background: var(--color-white);
  box-shadow: 0 3px 24px rgb(0 0 0 / 10%);
}

.ui-widget-header .ui-icon{
  background-image: unset;
}

.ui-datepicker .ui-widget-header{
  background: var(--theme-color);
  color: var(--color-white);
  border: none;
  border-radius: 15px;
}

.ui-datepicker .ui-datepicker-prev::after {
  content: "\f100";
  left: 5px;
  position: absolute;
  font-family: "Font Awesome 6 Pro";
  color: var(--color-white);
}

.ui-datepicker .ui-datepicker-next::after {
  content: "\f101";
  right: 5px;
  position: absolute;
  font-family: "Font Awesome 6 Pro";
  color: var(--color-white);
}

.ui-datepicker .ui-datepicker-header .ui-corner-all,
.ui-datepicker .ui-datepicker-header .ui-state-hover {
  cursor: pointer;
  border: 0;
  background: none;
  font-weight: 600;
  top: 3px;
}

.ui-datepicker td span,
.ui-datepicker td a {
  display: block;
  padding: 0.2em;
  text-align: center;
  text-decoration: none;
}

.ui-state-default,
.ui-widget-content .ui-state-default{
  border: none;
  border-radius: 10px;
}

.ui-state-highlight, .ui-widget-content .ui-state-highlight {
  border: none;
  background: var(--theme-color2);
  color: var(--color-white);
}

.ui-state-active,
.ui-widget-content .ui-state-active{
  background: var(--theme-color);
  color: var(--color-white);
}



/* ===================
13. Time picker css 
====================== */

.ui-timepicker.ui-widget.ui-widget-content{
  box-shadow: none;
}

.ui-timepicker-standard{
  border: none !important;
  background: var(--color-white);
  border-radius: 10px;
  box-shadow: var(--box-shadow);
  z-index: 2 !important;
}

.ui-timepicker-standard a{
  border: none !important;
  transition: none !important;
  border-radius: 8px;
}

.ui-timepicker-standard a#ui-active-item,
.ui-timepicker-standard a:hover{
  background: var(--theme-color);
  color: var(--color-white);
}




/* =====================
14. Price range slider
===================== */

.price-range-slider{
  margin-bottom: 50px;
}

.price-range-slider .price-range-info{
  margin-bottom: 20px;
}

.priceRange {
  background: transparent;
  border: none;
  font-weight: 800;
  outline: none;
  color: var(--theme-color);
}

.price-range-slider label {
  color: var(--color-dark);
  font-weight: 500;
}

.price-range-slider .ui-slider-handle {
  top: -0.36em !important;
  border-radius: 50px;
  background: var(--color-white) !important;
  border: 4px solid var(--theme-color) !important;
  width: 1.1em;
  height: 1.1em;
  outline: none;
}

.price-range-slider .ui-widget.ui-widget-content {
  background: #E6E9EC;
  border: none;
  border-radius: 50px;
  padding: 0;
  height: 0.4em;
}

.price-range-slider .ui-widget-header {
  background: var(--theme-color);
}




/* =====================
15. Play btn
===================== */

.play-btn {
  display: inline-block;
  padding: 0;
  height: 75px;
  width: 75px;
  line-height: 75px;
  font-size: 20px;
  text-align: center;
  background: var(--theme-color2);
  color: var(--color-white) !important;
  position: absolute;
  border-radius: 50%;
  z-index: 1;
}

.play-btn i::after {
  content: "";
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  z-index: -1;
  background-color: var(--theme-color2);
  border-radius: 50px;
  animation: ripple-wave 1s linear infinite;
  -webkit-transform: scale(1);
  transform: scale(1);
  transition: all 0.5s ease-in-out;
}

@keyframes ripple-wave {
  0% {
    opacity: 0.8;
    -webkit-transform: scale(0.9);
    transform: scale(0.9);
  }

  100% {
    opacity: 0;
    -webkit-transform: scale(2);
    transform: scale(2);
  }
}




/* ===================
16. Destination css 
====================== */

.destination-area{
  position: relative;
}

.destination-item {
  margin-bottom: 25px;
  position: relative;
}

.destination-img{
  overflow: hidden;
  border-radius: 200px;
  position: relative;
}

.destination-img img {
  border-radius: 200px;
  width: 100%;
  transition: var(--transition);
}

.destination-item:hover .destination-img img {
  transform: scale(1.1);
}

.destination-btn {
  position: absolute;
  width: 55px;
  height: 55px;
  line-height: 56px;
  text-align: center;
  background: var(--color-white);
  color: var(--theme-color) !important;
  border-radius: 50px;
  opacity: 0;
  visibility: hidden;
  top: 50%;
  left: 50%;
  font-size: 22px;
  transform: translate(-50%, -50%);
  box-shadow: 0 3px 24px rgb(0 0 0 / 10%);
  transition: var(--transition);
  z-index: 1;
}

.destination-item:hover .destination-btn {
  visibility: visible;
  opacity: 1;
}

.destination-info{
  position: relative;
  width: 80%;
  background: var(--color-white);
  text-align: center;
  border-radius: 100px;
  padding: 10px 20px;
  margin: -80px auto 0 auto;
  box-shadow: var(--box-shadow);
}

.destination-info h4 a{
  color: var(--color-dark);
  font-size: 20px;
}

.destination-info h4 a:hover{
  color: var(--theme-color);
}

.destination-rate{
  font-size: 15px;
  margin-top: 5px;
}

.destination-rate i{
  color: #FFA903;
}

.destination-rate span {
  color: var(--color-dark);
  font-weight: 400;
}

.destination-more-info{
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.destination-more-info span{
  color: var(--body-text-color);
}

.destination-more-info span i{
  color: var(--theme-color);
}

.destination-slider.owl-theme .owl-nav {
  margin-top: 0px;
}

.destination-slider.owl-theme .owl-nav button{
  color: var(--theme-color);
  font-size: 25px;
  margin: 0;
  padding: 0;
  background: var(--color-white);
  display: inline-block;
  cursor: pointer;
  height: 45px;
  width: 45px;
  border-radius: 50px;
  line-height: 45px;
  text-align: center;
  box-shadow: 0 3px 24px rgb(0 0 0 / 10%);
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
  transition: var(--transition);
  margin-top: -50px;
}

.destination-slider.owl-theme .owl-nav button:hover{
  background: var(--theme-color);
  color: var(--color-white);
}

.destination-slider.owl-theme .owl-nav .owl-prev {
  left: -25px;
}

.destination-slider.owl-theme .owl-nav .owl-next {
  right: -25px;
}

.destination-slider .owl-dots {
  text-align: center;
  margin-top: 10px;
}

.destination-slider .owl-dots .owl-dot span {
  background: transparent;
  margin: 5px;
  border: 2px solid var(--theme-color);
  border-radius: 50px;
  width: 12px;
  height: 12px;
  display: inline-block;
  transition: var(--transition);
}

.destination-slider .owl-dots .owl-dot.active span {
  background: var(--theme-color);
}


@media all and (max-width: 1399px) {
  .destination-info h4 {
    font-size: 23px;
  }
}

@media all and (max-width: 767px) {
  .destination-item img {
    height: 400px;
  }
  .destination-slider.owl-theme .owl-nav {
    display: none;
  }
}




/* ===================
17. Flight css 
====================== */

.flight-area{
  position: relative;
}

.flight-item{
  background: var(--color-white);
  padding: 15px;
  border-radius: 40px 40px 40px 0;
  margin-bottom: 25px;
  box-shadow: var(--box-shadow);
  position: relative;
}

.flight-item .badge{
  background: var(--theme-color);
  border-radius: 50px 50px 50px 0;
  padding: 8px 15px;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: var(--box-shadow);
  position: absolute;
  right: -10px;
  top: 35px;
  z-index: 1;
}

.flight-item .badge-discount{
  background: var(--theme-color2);
}

.flight-item .add-wishlist{
  width: 40px;
  height: 40px;
  line-height: 36px;
  background: var(--theme-color);
  border: 3px solid var(--color-white);
  color: var(--color-white);
  border-radius: 50px;
  text-align: center;
  position: absolute;
  left: 12px;
  top: 12px;
  z-index: 1;
}

.flight-img{
  overflow: hidden;
  border-radius: 30px 30px 30px 0;
}

.flight-img img{
  border-radius: 30px 30px 30px 0;
}

.flight-item:hover .flight-img img{
  transform: scale(1.1);
}

.flight-title{
  margin-top: 15px;
}

.flight-title-info{
  display: flex;
  align-items: center;
  gap: 7px;
}

.flight-title-info img{
  width: 20px;
}

.flight-title-info h4{
  font-size: 18px;
}

.flight-title-info a{
  color: var(--color-dark);
}

.flight-title-info a:hover{
  color: var(--theme-color);
}

.flight-title-info a i{
  margin: 0 5px;
  font-size: 16px;
  color: var(--theme-color);
}

.flight-date{
  margin-top: 10px;
  font-weight: 500;
  color: var(--color-dark);
}

.flight-date i{
  margin-right: 5px;
  color: var(--theme-color);
}

.flight-bottom{
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 15px;
  padding-top: 10px;
  border-top: 1px solid rgba(0, 0, 0, .05);
}

.flight-price{
  color:var(--color-dark);
  font-weight: 500;
}

.flight-price span{
  font-weight: 700;
  color: var(--theme-color2);
  font-size: 19px;
}

.flight-text-btn a{
  color: var(--color-dark);
  font-weight: 500;
}

.flight-text-btn a i{
  font-size: 14px;
}

.flight-text-btn a:hover{
  color: var(--theme-color);
}



/* ===================
18. Hotel css 
====================== */

.hotel-area{
  position: relative;
}

.hotel-area.bg::before{
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  background-image: url(../img/shape/02.png);
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  opacity: .3;
}

.hotel-item{
  background: var(--color-white);
  border-radius: 40px 40px 40px 0;
  margin-bottom: 25px;
  padding: 5px;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
}

.hotel-item:hover{
  transform: translateY(4px);
}

.hotel-img{
  border-radius: 35px 35px 35px 0;
  position: relative;
}

.hotel-img img{
  border-radius: 35px 35px 35px 0;
}

.hotel-img .badge{
  background: var(--theme-color);
  border-radius: 50px 50px 50px 0;
  padding: 8px 15px;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: var(--box-shadow);
  position: absolute;
  right: 20px;
  top: -15px;
}

.hotel-img .badge-discount{
  background: var(--theme-color2);
}

.hotel-img .add-wishlist{
  width: 40px;
  height: 40px;
  line-height: 35px;
  background: var(--theme-color);
  border: 3px solid var(--color-white);
  color: var(--color-white);
  border-radius: 50px 50px 50px 0;
  text-align: center;
  position: absolute;
  left: -5px;
  bottom: -5px;
}

.hotel-img .add-wishlist:hover{
  background: var(--theme-color2);
  color: var(--color-white);
}

.hotel-content{
  padding: 20px 15px 10px 15px;
}

.hotel-title a:hover{
  color: var(--theme-color);
}

.hotel-content p{
  margin: 8px 0;
  color: var(--color-dark);
  font-weight: 500;
}

.hotel-content p i{
  color: var(--theme-color);
}

.hotel-rate .badge{
  background: #FFA903;
  font-size: 13px;
  border-radius: 50px 50px 50px 0;
}

.hotel-rate-type{
  color: var(--theme-color);
  font-weight: 500;
  margin: 0 5px;
}

.hotel-rate-review{
  color: var(--color-dark);
  font-weight: 500;
}

.hotel-bottom{
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top: 1px solid rgba(0, 0, 0, .05);
  margin-top: 20px;
  padding-top: 13px;
}

.hotel-price-amount{
  color: var(--theme-color2);
  font-weight: 700;
  font-size: 19px;
}

.hotel-price-type{
  font-size: 14px;
  font-weight: 500;
  color: var(--color-dark);
}

.hotel-text-btn a {
  color: var(--color-dark);
  font-weight: 500;
}

.hotel-text-btn a i {
  font-size: 14px;
}

.hotel-text-btn a:hover{
  color: var(--theme-color);
}

.hotel-slider .hotel-item{
  margin-top: 10px;
  margin-bottom: 10px;
  box-shadow: none;
}

.hotel-slider.owl-theme .owl-nav {
  margin-top: 0px;
}

.hotel-slider.owl-theme .owl-nav button{
  color: var(--theme-color);
  font-size: 25px;
  margin: 0;
  margin-top: -28px;
  padding: 0;
  background: var(--color-white);
  display: inline-block;
  cursor: pointer;
  height: 45px;
  width: 45px;
  border-radius: 50px;
  line-height: 45px;
  text-align: center;
  box-shadow: 0 3px 24px rgb(0 0 0 / 10%);
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
  transition: var(--transition);
}

.hotel-slider.owl-theme .owl-nav button:hover{
  background: var(--theme-color);
  color: var(--color-white);
}

.hotel-slider.owl-theme .owl-nav .owl-prev {
  left: -25px;
}

.hotel-slider.owl-theme .owl-nav .owl-next {
  right: -25px;
}

.hotel-slider .owl-dots {
  text-align: center;
  margin-top: 30px;
}

.hotel-slider .owl-dots .owl-dot span {
  background: transparent;
  margin: 5px;
  border: 2px solid var(--theme-color);
  border-radius: 50px;
  width: 12px;
  height: 12px;
  display: inline-block;
  transition: var(--transition);
}

.hotel-slider .owl-dots .owl-dot.active span {
  background: var(--theme-color);
}

/* hotel list */
.hotel-list .hotel-item{
  display: flex;
  align-items: center;
  gap: 20px;
}

.hotel-list .hotel-img{
  width: 350px;
}

.hotel-list .hotel-content{
  flex: 1;
}

/* hotel single */
.hotel-single .room-list .room-img{
  width: 360px;
}

@media all and (max-width: 767px) {
  .hotel-slider.owl-theme .owl-nav {
    display: none;
  }

  .hotel-list .hotel-item {
    display: block;
  }

  .hotel-list .hotel-img{
    width: 100%;
  }
}



/* ===================
19. Room type css 
====================== */

.room-type-item{
  position: relative;
  margin-bottom: 25px;
}

.room-type-item::before{
  content: "";
  position: absolute;
  background: rgba(13, 44, 122, .4);
  width: 100%;
  height: 100%;
  border-radius: 100px 100px 100px 0;
  left: 0;
  top: 0;
}

.room-type-item img{
  border-radius: 100px 100px 100px 0;
}

.room-type-item .theme-btn{
  position: absolute;
  left: 50%;
  top: 50%;
  background: var(--color-white);
  color: var(--color-dark);
  transform: translate(-50%, -50%);
}

.room-type-item .theme-btn:hover{
  color: var(--color-white);
}


/* ===================
20. Room css 
====================== */
.room-area{
  position: relative;
}

.room-item{
  background: var(--color-white);
  border-radius: 40px 40px 40px 0;
  padding: 5px;
  margin-bottom: 25px;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
}

.room-item:hover{
  transform: translateY(4px);
}

.room-img{
  border-radius: 35px 35px 35px 0;
  position: relative;
}

.room-img img{
  border-radius: 35px 35px 35px 0;
}

.room-img .badge{
  background: var(--theme-color);
  border-radius: 50px 50px 50px 0;
  padding: 8px 15px;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: var(--box-shadow);
  position: absolute;
  right: 20px;
  top: -15px;
}

.room-img .badge-discount{
  background: var(--theme-color2);
}

.room-img .add-wishlist{
  width: 35px;
  height: 35px;
  line-height: 35px;
  background: var(--theme-color);
  color: var(--color-white);
  border-radius: 50px;
  text-align: center;
  position: absolute;
  left: 10px;
  top: 10px;
}

.room-img .add-wishlist:hover{
  background: var(--theme-color2);
  color: var(--color-white);
}

.room-content{
  padding: 20px 15px 10px 15px;
}

.room-title a:hover{
  color: var(--theme-color);
}

.room-content p{
  margin: 8px 0;
  color: var(--color-dark);
  font-weight: 500;
}

.room-rate .badge{
  background: #FFA903;
  font-size: 13px;
}

.room-rate-type{
  color: var(--theme-color);
  font-weight: 500;
  margin: 0 5px;
}

.room-rate-review{
  color: var(--color-dark);
  font-weight: 500;
}

.room-info-list{
  margin-top: 10px;
}

.room-info-list li{
  display: inline-block;
  margin-right: 15px;
  margin-top: 8px;
}

.room-info-list i{
  color: var(--theme-color);
  margin-right: 6px;
}

.room-detail-btn a{
  margin-top: 8px;
  text-decoration: underline;
  color: var(--theme-color);
}

.room-detail-btn a:hover{
  color: var(--theme-color2);
}

.room-bottom{
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top: 1px solid rgba(0, 0, 0, .05);
  margin-top: 20px;
  padding-top: 13px;
}

.room-price-amount{
  color: var(--theme-color2);
  font-weight: 700;
  font-size: 19px;
}

.room-price-type{
  font-size: 14px;
  font-weight: 500;
  color: var(--color-dark);
}

.room-text-btn a {
  color: var(--color-dark);
  font-weight: 500;
}

.room-text-btn a i {
  font-size: 14px;
}

.room-text-btn a:hover{
  color: var(--theme-color);
}

.room-select-btn{
  background: var(--theme-color2);
  border-radius: 15px;
  padding: 5px 12px;
  box-shadow: var(--box-shadow);
}

.room-select-btn .form-check-input{
  margin-top: 7px;
  box-shadow: none;
  border: none;
  border-radius: 6px;
}

.room-select-btn .form-check-label{
  color: var(--color-white);
  font-weight: 500;
}


/* room list */
.room-list .room-item{
  display: flex;
  align-items: center;
  gap: 25px;
}

.room-list .room-img{
  width: 350px;
}

.room-list .room-content{
  flex: 1;
  padding: 10px 25px 10px 0;
}

@media all and (min-width: 992px) {
  .room-list .room-img{
    width: 300px;
  }

  .room-list .room-info-list{
    width: 400px;
  }
}

@media all and (max-width: 767px) {
  .room-list .room-item {
    display: block;
  }

  .room-list .room-img{
    width: 100% !important;
  }

  .room-list .room-content{
    padding: 20px;
  }

  .room-list .room-info-list{
    width: 100%;
  }
}



/* ===================
21. Banner css 
====================== */

.banner-area{
  position: relative;
}

.banner-item{
  position: relative;
  display: flex;
  align-items: center;
  gap: 18px;
  border-radius: 35px;
  background: var(--color-white);
  border: 1px solid var(--border-info-color);
}

.banner-area.bg .banner-item{
  border: none;
}

.banner-img img{
  width: 300px;
  height: 170px;
  object-fit: cover;
  border-radius: 35px 0 0 35px;
}

.banner-content{
  padding: 10px 15px 10px 0;
}

.banner-content h6{
  font-size: 18px;
  margin-bottom: 8px;
}

.banner-content h6 span{
  color: var(--theme-color);
}

.banner-content p{
  color: var(--body-text-color);
  margin: 0 0 12px 0;
}

.banner-content .theme-btn{
  padding: 2px 10px 2px 12px;
  vertical-align: middle;
} 

.banner-slider.owl-theme .owl-nav {
  margin-top: 0px;
}

.banner-slider.owl-theme .owl-nav button{
  color: var(--theme-color);
  font-size: 25px;
  margin: 0;
  margin-top: -28px;
  padding: 0;
  background: var(--color-white);
  display: inline-block;
  cursor: pointer;
  height: 45px;
  width: 45px;
  border-radius: 50px;
  line-height: 45px;
  text-align: center;
  box-shadow: 0 3px 24px rgb(0 0 0 / 10%);
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
  transition: var(--transition);
}

.banner-slider.owl-theme .owl-nav button:hover{
  background: var(--theme-color);
  color: var(--color-white);
}

.banner-slider.owl-theme .owl-nav .owl-prev {
  left: -25px;
}

.banner-slider.owl-theme .owl-nav .owl-next {
  right: -25px;
}

.banner-slider .owl-dots {
  text-align: center;
  margin-top: 30px;
}

.banner-slider .owl-dots .owl-dot span {
  background: transparent;
  margin: 5px;
  border: 2px solid var(--theme-color);
  border-radius: 50px;
  width: 12px;
  height: 12px;
  display: inline-block;
  transition: var(--transition);
}

.banner-slider .owl-dots .owl-dot.active span {
  background: var(--theme-color);
}

@media all and (max-width: 991px) {
  .banner-item{
    flex-direction: column;
  }

  .banner-img img{
    border-radius: 35px 35px 0 0;
  }

  .banner-content{
    padding: 0 20px 20px 20px;
  }
}




/* ===================
22. Tour css 
====================== */

.tour-area{
  position: relative;
}

.tour-item{
  background: var(--color-white);
  border-radius: 40px 40px 40px 0;
  padding: 5px;
  margin-bottom: 25px;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
}

.tour-item:hover{
  transform: translateY(4px);
}

.tour-img{
  border-radius: 35px 35px 35px 0;
  position: relative;
}

.tour-img img{
  border-radius: 35px 35px 35px 0;
}

.tour-img .badge{
  background: var(--theme-color);
  border-radius: 50px 50px 50px 0;
  padding: 8px 15px;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: var(--box-shadow);
  position: absolute;
  right: 20px;
  top: -15px;
}

.tour-img .badge-discount{
  background: var(--theme-color2);
}

.tour-img .add-wishlist{
  width: 40px;
  height: 40px;
  line-height: 35px;
  text-align: center;
  background: var(--theme-color);
  border: 3px solid var(--color-white);
  color: var(--color-white);
  border-radius: 50px 50px 50px 0;
  position: absolute;
  left: -5px;
  bottom: -5px;
}

.tour-img .add-wishlist:hover{
  background: var(--theme-color2);
  color: var(--color-white);
}

.tour-top{
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
  color: var(--color-dark);
  font-weight: 500;
}

.tour-top i{
  color: var(--theme-color);
}

.tour-top a:hover{
  color: var(--theme-color);
}

.tour-content{
  padding: 15px 15px 10px 15px;
}

.tour-title a:hover{
  color: var(--theme-color);
}

.tour-content p{
  margin: 8px 0;
  color: var(--color-dark);
  font-weight: 500;
}

.tour-content p i{
  color: var(--theme-color);
}

.tour-rate .badge{
  background: #FFA903;
  font-size: 13px;
}

.tour-rate-type{
  color: var(--theme-color);
  font-weight: 500;
  margin: 0 5px;
}

.tour-rate-review{
  color: var(--color-dark);
  font-weight: 500;
}

.tour-duration{
  margin-top: 10px;
  color: var(--color-dark);
  font-weight: 500;
}

.tour-duration i{
  color: var(--theme-color);
}

.tour-bottom{
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top: 1px solid rgba(0, 0, 0, .05);
  margin-top: 20px;
  padding-top: 13px;
}

.tour-price {
  color: var(--color-dark);
  font-weight: 500;
}

.tour-price span{
  font-weight: 700;
  color: var(--theme-color2);
  font-size: 19px;
}

.tour-text-btn a {
  color: var(--color-dark);
  font-weight: 500;
}

.tour-text-btn a i {
  font-size: 14px;
}

.tour-text-btn a:hover{
  color: var(--theme-color);
}

.filter-controls{
  position: relative;
  border-radius: 50px 50px 0 50px;
  padding: 5px;
  background: var(--theme-color-light);
}

.filter-btns{
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.filter-btns li{
  background: var(--color-white);
  color: var(--color-dark);
  padding: 5px 20px;
  border-radius: 50px 50px 0 50px;
  font-weight: 500;
  cursor: pointer;
  box-shadow: var(--box-shadow);
}

.filter-btns li.active{
  background: var(--theme-color);
  color: var(--color-white);
}

/* tour list */
.tour-list .tour-item{
  display: flex;
  align-items: center;
  gap: 20px;
}

.tour-list .tour-img{
  width: 410px;
}

.tour-list .tour-content{
  flex: 1;
}


@media all and (max-width: 767px) {
  .filter-btns{
    padding: 15px;
  }

  .tour-list .tour-item {
    display: block;
  }

  .tour-list .tour-img{
    width: 100%;
  }
}




/* ===================
23. Car css 
====================== */
.car-area{
  position: relative;
}

.car-area.bg::before{
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url(../img/shape/03.png);
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  opacity: .25;
}

.car-item{
  background: var(--color-white);
  border-radius: 40px 40px 40px 0;
  padding: 5px;
  margin-bottom: 25px;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
}

.car-item:hover{
  transform: translateY(4px);
}

.car-img{
  border-radius: 35px 35px 35px 0;
  position: relative;
}

.car-img img{
  border-radius: 35px 35px 35px 0;
}

.car-img .badge{
  background: var(--theme-color);
  border-radius: 50px 50px 50px 0;
  padding: 8px 15px;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: var(--box-shadow);
  position: absolute;
  right: 20px;
  top: -15px;
}

.car-img .badge-discount{
  background: var(--theme-color2);
}

.car-img .add-wishlist{
  width: 40px;
  height: 40px;
  line-height: 35px;
  background: var(--theme-color);
  border: 3px solid var(--color-white);
  color: var(--color-white);
  border-radius: 50px 50px 50px 0;
  text-align: center;
  position: absolute;
  left: -5px;
  bottom: -5px;
}

.car-img .add-wishlist:hover{
  background: var(--theme-color2);
  color: var(--color-white);
}

.car-content{
  padding: 20px 15px 10px 15px;
}

.car-title a:hover{
  color: var(--theme-color);
}

.car-content p{
  margin: 8px 0;
  color: var(--color-dark);
  font-weight: 500;
}

.car-rate .badge{
  background: #FFA903;
  font-size: 13px;
  border-radius: 50px 50px 50px 0;
}

.car-rate-type{
  color: var(--theme-color);
  font-weight: 500;
  margin: 0 5px;
}

.car-rate-review{
  color: var(--color-dark);
  font-weight: 500;
}

.car-info-list{
  margin-top: 10px;
}

.car-info-list li{
  display: inline-block;
  margin-right: 15px;
  margin-top: 8px;
}

.car-info-list i{
  color: var(--theme-color);
  margin-right: 6px;
}

.car-bottom{
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top: 1px solid rgba(0, 0, 0, .05);
  margin-top: 20px;
  padding-top: 13px;
}

.car-price-amount{
  color: var(--theme-color2);
  font-weight: 700;
  font-size: 19px;
}

.car-price-type{
  font-size: 14px;
  font-weight: 500;
  color: var(--color-dark);
}

.car-text-btn a {
  color: var(--color-dark);
  font-weight: 500;
}

.car-text-btn a i {
  font-size: 14px;
}

.car-text-btn a:hover{
  color: var(--theme-color);
}

.car-slider .car-item{
  margin-top: 10px;
  margin-bottom: 10px;
  box-shadow: none;
}

.car-slider.owl-theme .owl-nav {
  margin-top: 0px;
}

.car-slider.owl-theme .owl-nav button{
  color: var(--theme-color);
  font-size: 25px;
  margin: 0;
  margin-top: -25px;
  padding: 0;
  background: var(--color-white);
  display: inline-block;
  cursor: pointer;
  height: 45px;
  width: 45px;
  border-radius: 50px;
  line-height: 45px;
  text-align: center;
  box-shadow: 0 3px 24px rgb(0 0 0 / 10%);
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
  transition: var(--transition);
}

.car-slider.owl-theme .owl-nav button:hover{
  background: var(--theme-color);
  color: var(--color-white);
}

.car-slider.owl-theme .owl-nav .owl-prev {
  left: -25px;
}

.car-slider.owl-theme .owl-nav .owl-next {
  right: -25px;
}

.car-slider .owl-dots {
  text-align: center;
  margin-top: 30px;
}

.car-slider .owl-dots .owl-dot span {
  background: transparent;
  margin: 5px;
  border: 2px solid var(--theme-color);
  border-radius: 50px;
  width: 12px;
  height: 12px;
  display: inline-block;
  transition: var(--transition);
}

.car-slider .owl-dots .owl-dot.active span {
  background: var(--theme-color);
}

/* car list */
.car-list .car-item{
  display: flex;
  align-items: center;
  gap: 20px;
}

.car-list .car-img{
  width: 410px;
}

.car-list .car-content{
  flex: 1;
}


@media all and (max-width: 767px) {
  .car-slider.owl-theme .owl-nav {
    display: none;
  }

  .car-list .car-item {
    display: block;
  }

  .car-list .car-img{
    width: 100%;
  }
}



/* ===================
24. Activity css 
====================== */

.activity-area{
  position: relative;
}

.activity-item{
  background: var(--color-white);
  border-radius: 40px 40px 40px 0;
  margin-bottom: 25px;
  padding: 5px;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
}

.activity-item:hover{
  transform: translateY(4px);
}

.activity-img{
  border-radius: 35px 35px 35px 0;
  position: relative;
}

.activity-img img{
  border-radius: 35px 35px 35px 0;
}

.activity-img .badge{
  background: var(--theme-color);
  border-radius: 50px 50px 50px 0;
  padding: 8px 15px;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: var(--box-shadow);
  position: absolute;
  right: 20px;
  top: -15px;
}

.activity-img .badge-discount{
  background: var(--theme-color2);
}

.activity-img .add-wishlist{
  width: 40px;
  height: 40px;
  line-height: 35px;
  background: var(--theme-color);
  border: 3px solid var(--color-white);
  color: var(--color-white);
  border-radius: 50px 50px 50px 0;
  text-align: center;
  position: absolute;
  left: -5px;
  bottom: -5px;
}

.activity-img .add-wishlist:hover{
  background: var(--theme-color2);
  color: var(--color-white);
}

.activity-content{
  padding: 20px 15px 10px 15px;
}

.activity-title a:hover{
  color: var(--theme-color);
}

.activity-content p{
  margin: 8px 0;
  color: var(--color-dark);
  font-weight: 500;
}

.activity-content p i{
  color: var(--theme-color);
}

.activity-rate .badge{
  background: #FFA903;
  font-size: 13px;
  border-radius: 50px 50px 50px 0;
}

.activity-rate-type{
  color: var(--theme-color);
  font-weight: 500;
  margin: 0 5px;
}

.activity-rate-review{
  color: var(--color-dark);
  font-weight: 500;
}

.activity-bottom{
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top: 1px solid rgba(0, 0, 0, .05);
  margin-top: 20px;
  padding-top: 13px;
}

.activity-price-amount{
  color: var(--theme-color2);
  font-weight: 700;
  font-size: 19px;
}

.activity-text-btn a {
  color: var(--color-dark);
  font-weight: 500;
}

.activity-text-btn a i {
  font-size: 14px;
}

.activity-text-btn a:hover{
  color: var(--theme-color);
}

/* activity list */
.activity-list .activity-item{
  display: flex;
  align-items: center;
  gap: 20px;
}

.activity-list .activity-img{
  width: 350px;
}

.activity-list .activity-content{
  flex: 1;
}


@media all and (max-width: 767px) {
  .activity-list .activity-item {
    display: block;
  }

  .activity-list .activity-img{
    width: 100%;
  }
}





/* ===================
25. Cruise css 
====================== */

.cruise-area{
  position: relative;
}

.cruise-item{
  background: var(--color-white);
  border-radius: 40px 40px 40px 0;
  padding: 5px;
  margin-bottom: 25px;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
}

.cruise-item:hover{
  transform: translateY(4px); 
}

.cruise-img{
  border-radius: 35px 35px 35px 0;
  position: relative;
}

.cruise-img img{
  border-radius: 35px 35px 35px 0;
}

.cruise-img .badge{
  background: var(--theme-color);
  border-radius: 50px 50px 50px 0;
  padding: 8px 15px;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: var(--box-shadow);
  position: absolute;
  right: 20px;
  top: -15px;
}

.cruise-img .badge-discount{
  background: var(--theme-color2);
}

.cruise-img .add-wishlist{
  width: 40px;
  height: 40px;
  line-height: 35px;
  background: var(--theme-color);
  border: 3px solid var(--color-white);
  color: var(--color-white);
  border-radius: 50px 50px 50px 0;
  text-align: center;
  position: absolute;
  left: -5px;
  bottom: -5px;
}

.cruise-img .add-wishlist:hover{
  background: var(--theme-color2);
  color: var(--color-white);
}

.cruise-content{
  padding: 20px 15px 10px 15px;
}

.cruise-title a:hover{
  color: var(--theme-color);
}

.cruise-content p{
  margin: 8px 0;
  color: var(--color-dark);
  font-weight: 500;
}

.cruise-rate .badge{
  background: #FFA903;
  font-size: 13px;
  border-radius: 50px 50px 50px 0;
}

.cruise-rate-type{
  color: var(--theme-color);
  font-weight: 500;
  margin: 0 5px;
}

.cruise-rate-review{
  color: var(--color-dark);
  font-weight: 500;
}

.cruise-info-list{
  margin-top: 10px;
}

.cruise-info-list li{
  display: inline-block;
  margin-right: 15px;
  margin-top: 8px;
}

.cruise-info-list i{
  color: var(--theme-color);
  margin-right: 6px;
}

.cruise-bottom{
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top: 1px solid rgba(0, 0, 0, .05);
  margin-top: 20px;
  padding-top: 13px;
}

.cruise-price-amount{
  color: var(--theme-color2);
  font-weight: 700;
  font-size: 19px;
}

.cruise-price-type{
  font-size: 14px;
  font-weight: 500;
  color: var(--color-dark);
}

.cruise-text-btn a {
  color: var(--color-dark);
  font-weight: 500;
}

.cruise-text-btn a i {
  font-size: 14px;
}

.cruise-text-btn a:hover{
  color: var(--theme-color);
}


/* cruise list */
.cruise-list .cruise-item{
  display: flex;
  align-items: center;
  gap: 20px;
}

.cruise-list .cruise-img{
  width: 410px;
}

.cruise-list .cruise-content{
  flex: 1;
}


@media all and (max-width: 767px) {
  .cruise-list .cruise-item {
    display: block;
  }

  .cruise-list .cruise-img{
    width: 100%;
  }

}



/* ===================
26. Listing single css 
====================== */

.listing-header{
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
}

.listing-title{
  font-size: 25px;
}

.listing-location{
  color: var(--color-dark);
  margin: 5px 0;
}

.listing-location i{
  color: var(--theme-color);
}

.listing-rate .badge{
  background: #FFA903;
  margin-right: 5px;
  border-radius: 50px 50px 50px 0;
}

.listing-rate-type{
  color: var(--theme-color);
  font-weight: 500;
}

.listing-rate-review{
  color: var(--color-dark);
}

.listing-item{
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid rgba(0, 0, 0, .05);
}

.listing-feature{
  display: flex;
  gap: 10px;
}

.listing-feature-icon{
  width: 45px;
  height: 45px;
  line-height: 45px;
  background: var(--theme-color);
  border-radius: 50px 50px 50px 0;
  font-size: 18px;
  text-align: center;
  color: var(--color-white);
}

.listing-feature-content span{
  color: var(--color-dark);
}

.listing-highlight{
  margin: 25px 0;
}

.listing-highlight h5{
  margin-bottom: 15px;
}

.listing-item .list li{
  margin-bottom: 15px;
  padding-left: 18px;
  position: relative;
}

.listing-item .list li::before{
  content: "\e122";
  position: absolute;
  font-family: "Font Awesome 6 pro";
  font-size: 10px;
  color: var(--theme-color);
  font-weight: bold;
  top: 6.7px;
  left: 0;
}

.listing-info{
  margin-top: 20px;
  margin-bottom: 20px;
}

.listing-info h5{
  margin-bottom: 15px;
}

.listing-info .list li{
  padding-left: 0;
}

.listing-info .list li span{
  margin-right: 10px;
  color: var(--color-dark);
}

.listing-info .list li::before{
  display: none;
}

.listing-included h5{
  margin-bottom: 15px;
}

.listing-included .list li{
  padding-left: 24px;
}

.listing-included .list li::before{
  content: "\f00c";
  font-size: 16px;
  top: 0px;
  color: var(--color-green);
}

.listing-notincluded h5{
  margin-bottom: 15px;
}

.listing-notincluded .list li::before{
  content: "\f00d";
  font-size: 16px;
  top: 0px;
  color: var(--color-red);
}

.listing-item iframe{
  border-radius: 25px;
  margin-bottom: 15px;
}

.listing-item.faq-area{
  margin-bottom: 35px;
}

.listing-item.faq-area .accordion-item{
  box-shadow: none;
  border: 1px solid rgba(0, 0, 0, .08);
}

.listing-item.faq-area .accordion-body{
  padding-top: 0;
}

.listing-item.faq-area .accordion-button:not(.collapsed){
  border: none;
}

.listing-amenity-item .list li::before{
  content: "\f00c";
  font-size: 14px;
  top: 0px;
  left: 4px;
  color: var(--color-green);
}

.listing-amenity-item h6{
  margin-bottom: 15px;
  margin-top: 15px;
}

.listing-amenity-item h6 i{
  width: 30px;
  height: 30px;
  line-height: 30px;
  margin-right: 8px;
  border-radius: 50px 50px 50px 0;
  font-size: 14px;
  text-align: center;
  color: var(--color-white);
  background: var(--theme-color);
}

.listing-amenity-item .list li{
  margin-bottom: 5px;
  padding-left: 25px;
  font-size: 15px;
}

.listing-stop{
  font-weight: 500;
}

.listing-stop .badge{
  background: var(--theme-color);
}

.listing-flight .flight-booking-time .start-time,
.listing-flight .flight-booking-time .end-time{
  display: flex;
  align-items: center;
  gap: 12px;
}

.listing-flight .flight-booking-time .start-time-text,
.listing-flight .flight-booking-time .end-time-text{
  color: var(--color-dark);
  font-weight: 700;
}

.listing-flight .flight-destination{
  color: var(--color-dark);
  font-weight: 500;
}

.listing-flight .flight-booking-time{
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 30px;
}

.listing-flight .start-time-icon{
  font-size: 25px;
  color: var(--color-dark);
}

.listing-flight .flight-stop{
  text-align: center;
}

.listing-flight .flight-stop-number{
  color: var(--color-dark);
  font-weight: 500;
}

.listing-flight .flight-stop-arrow{
  margin-top: 5px;
  border-top: 2px solid var(--color-dark);
  position: relative;
  width: 140px;
}

.listing-flight .flight-stop-arrow::before{
  content: "";
  position: absolute;
  border-style: solid;
  border-width: 10px 10px 0 0;
  border-color: var(--color-dark) transparent transparent transparent;
  right: -1.5px;
  top: -9.6px;
  transform: scaleY(-1);
}

.listing-flight .flight-has-stop::after{
  content: "\e122";
  position: absolute;
  left: 50%;
  top: -15px;
  font-family: "Font Awesome 6 Pro";
  color:var(--color-dark);
  font-weight: bold;
}

.listing-flight .flight-booking-return .flight-has-stop::after{
  top: -16px;
}

.listing-flight .flight-booking-duration .duration-text{
  color: var(--color-dark);
  font-weight: 500;
}

.listing-baggage{
  margin-top: 30px;
  margin-bottom: 20px;
  padding-top: 30px;
  border-top: 1px solid rgba(0, 0, 0, .08);
}

.listing-baggage-check .form-group{
  margin-bottom: 25px;
}

.listing-baggage-check .form-group label{
  color: var(--color-dark);
  margin-bottom: 5px;
}

.listing-baggage-check .form-group-icon{
  position: relative;
}

.listing-baggage-check .form-group-icon i{
  position: absolute;
  left: 18px;
  top: 20px;
  color: var(--theme-color);
}

.listing-baggage-check .form-control{
  padding: 15px 18px 15px 45px;
  border-radius: 15px;
  box-shadow: none;
  color: var(--color-dark);
  border-color: var(--border-info-color);
}

.listing-baggage-check .form-control::placeholder{
  color: var(--body-text-color);
}

.listing-baggage-check .nice-select{
  width: 100%;
  height: 55px;
  line-height: 55px;
  border-radius: 15px;
  border-color: var(--border-info-color);
  padding-left: 45px;
  font-size: 16px;
  color: var(--color-dark);
  margin-bottom: 25px;
}

.listing-baggage-check .form-control:focus,
.listing-baggage-check .nice-select:focus{
  border-color: var(--theme-color);
}

.listing-baggage-check .nice-select .list{
  width: 100%;
  border-radius: 12px;
}

.listing-baggage-check .nice-select:after{
  width: 9px;
  height: 9px;
  margin-top: -7px;
  right: 15px;
}


/* listing slider */
.listing-slider{
  overflow: hidden;
  border-radius: 25px;
}

.listing-slider img{
  border-radius: 25px;
}

.listing-slider.owl-theme .owl-nav {
  margin-top: 0px;
}

.listing-slider.owl-theme .owl-nav .owl-prev,
.listing-slider.owl-theme .owl-nav .owl-next {
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
  color: var(--theme-color);
  font-size: 20px;
  margin: 0;
  padding: 0;
  background: var(--color-white);
  display: inline-block;
  cursor: pointer;
  height: 45px;
  width: 45px;
  line-height: 45px;
  border-radius: 50px;
  text-align: center;
  transition: var(--transition);
}

.listing-slider.owl-theme .owl-nav .owl-prev:hover,
.listing-slider.owl-theme .owl-nav .owl-next:hover {
  background: var(--theme-color);
  color: var(--color-white);
}

.listing-slider.owl-theme .owl-nav .owl-prev {
  left: 20px;
}

.listing-slider.owl-theme .owl-nav .owl-next {
  right: 20px;
}


/* listing review */
.listing-review-rating{
  display: flex;
  align-items: center;
  gap: 20px;
}

.listing-rating-count{
  background: var(--color-white);
  text-align: center;
  padding: 20px 20px;
  border-radius: 25px;
  border: 1px solid rgba(0, 0, 0, .08);
}

.listing-rating-count h2{
  font-weight: 700;
  font-size: 50px;
  color: var(--theme-color);
}

.listing-rating-star i{
  color: #F8A611;
}

.listing-rating-count p{
  margin-top: 5px;
}

.listing-rating-range-star i{
  color: #F8A611;
  font-size: 14px;
}

.listing-rating-range-item{
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 5px 0;
}

.listing-progress{
  margin: 0px 15px;
  width: 300px;
  height: 5px;
  border-radius: 5px;
  position: relative;
  overflow: hidden;
  background-color: #E9ECEF;
}

.listing-progress-width{
  position: absolute;
  top: 0;
  left: 0;
  height: 5px;
  border-radius: 5px;
  background: var(--theme-color);
}

.listing-review{
  background: var(--color-white);
  border-radius: 5px;
  margin-top: 30px;
}

.listing-review h5{
  margin-bottom: 35px;
}

.listing-review-author{
  display: flex;
  align-items: center;
  gap: 10px;
}

.listing-review-author img{
  width: 60px;
  border-radius: 50%;
}

.listing-review-item{
  margin-bottom: 30px;
  border-radius: 25px;
  padding: 20px;
  border: 1px solid rgba(0, 0, 0, .08);
}

.review-reply-item{
  margin-left: 80px;
}

.listing-review-item p{
  margin-top: 10px;
}

.listing-review-author-info{
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.listing-review-author-rating i{
  color: #F8A611;
}

.listing-review-author-info span{
  font-size: 14px;
}

.listing-review-reply{
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
}

.review-reaction{
  display: flex;
  gap: 15px;
}

.listing-review-reply a:hover{
  color: var(--theme-color);
}

.review-reaction .active{
  color: var(--theme-color2);
}

.listing-review-form{
  padding: 30px;
  border-radius: 25px;
  margin-top: 30px;
  background: var(--theme-bg-light);
}

.listing-review-form h4{
  margin-bottom: 20px;
}

.listing-review-form .form-group{
  margin-bottom: 30px;
}

.listing-review-form .form-group .star-label{
  font-weight: 500;
}

.listing-review-form .form-control{
  box-shadow: none;
  padding: 15px 20px;
  border-radius: 15px;
}

.listing-review-form .form-control:focus{
  border-color: var(--theme-color);
}

.listing-info-card{
  background: var(--color-white);
  border-radius: 5px;
  padding: 20px;
  margin-bottom: 25px;
  box-shadow: var(--box-shadow);
}

.listing-info-card-body{
  display: flex;
  align-items: center;
  gap: 15px;
}

.listing-info-card-body i{
  color: var(--theme-color);
  font-size: 30px;
}

.listing-info-card p{
  margin-top: 10px;
}

/* star rating */
.star-rating-wrapper {
  display: inline-block;
}

.star-rating-box {
  direction: rtl !important;
}

.star-rating-box label {
  display: inline-block;
  color: #D4D4D4;
  cursor: pointer;
  font-size: 38px;
  transition: color 0.2s;
}

.star-rating-box input {
  display: none;
}

.star-rating-box label:hover,
.star-rating-box label:hover ~ label,
.star-rating-box input:checked ~ label {
  color: #F8A611;
}



/* itinerary */
.itinerary-content-box{
  margin-top: 50px;
  margin-bottom: 30px;
}

.itinerary-single-box {
  position: relative;
  padding-left: 110px;
  padding-bottom: 62px;
}

.itinerary-single-box::before {
  position: absolute;
  content: '';
  width: 1px;
  border-left: 2px dashed var(--theme-color);
  height: 100%;
  left: 31px;
  top: 10px;
  z-index: -1;
}

.itinerary-single-box img{
  width: 200px;
  border-radius: 50px 50px 50px 0;
  margin-bottom: 15px;
}

.itinerary-single-box:last-child::before{
  display: none;
}

.itinerary-single-box span {
  position: absolute;
  display: inline-block;
  left: 5px;
  top: 3px;
  width: 50px;
  height: 50px;
  line-height: 50px;
  font-size: 18px;
  color: var(--color-white);
  background: var(--theme-color);
  font-weight: 500;
  text-align: center;
  border-radius: 50px 50px 50px 0;
}

.itinerary-single-box span::before {
  position: absolute;
  content: '';
  background: var(--color-white);
  width: calc(100% + 10px);
  height: calc(100% + 10px);
  left: -5px;
  top: -5px;
  border-radius: 50%;
  box-shadow: 0px 10px 40px 0px rgb(0 0 0 / 20%);
  z-index: -1;
}

.itinerary-single-box h4{
  display: block;
  font-size: 17px;
  line-height: 28px;
  font-weight: 500;
  margin-bottom: 5px;
  color: var(--theme-color);
}

.itinerary-single-box h3 {
  display: block;
  font-size: 21px;
  line-height: 34px;
  font-weight: 600;
  margin-bottom: 10px;
}

.listing-price{
  margin-bottom: 10px;
}

.listing-price-tag{
  font-weight: 700;
  text-transform: uppercase;
  font-size: 16px;
  letter-spacing: 2px;
  color: var(--theme-color);
}

.listing-price-amount{
  margin-top: 10px;
  font-weight: 500;
}

.listing-price-amount span{
  font-weight: 600;
  font-size: 23px;
  color: var(--theme-color2);
  margin-left: 5px;
  margin-right: 5px;
}

.listing-side-content .search-form{
  margin-top: 25px;
}

.listing-side-content .search-form .form-group{
  margin-bottom: 20px;
  background: transparent;
  border: 1px solid rgba(0, 0, 0, .08);
}

.listing-side-btn .theme-btn,
.listing-side-btn .border-btn{
  width: 100%;
}

.listing-side-btn .border-btn{
  border: 2px solid var(--theme-color);
  margin-top: 25px;
  text-align: center;
  font-weight: 500;
  padding: 9px 20px;
  border-radius: 18px;
}

.listing-side-btn .border-btn:hover{
  background: var(--theme-color);
  color: var(--color-white);
}

.listing-side-share{
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 25px;
  color: var(--color-dark);
}

.listing-side-share a:hover{
  color: var(--theme-color);
}

.listing-side-list li{
  margin: 15px 0;
}

.listing-side-list li i{
  width: 30px;
  height: 30px;
  line-height: 30px;
  background: var(--theme-color);
  color: var(--color-white);
  font-size: 14px;
  margin-right: 10px;
  border-radius: 50px 50px 50px 0;
  text-align: center;
}

.listing-side-list li a{
  font-weight: 500;
}

.listing-side-list li a:hover{
  color: var(--theme-color);
}

.listing-side-organizer{
  text-align: center;
}

.listing-side-organizer-img{
  margin-bottom: 15px;
}

.listing-side-organizer-img img{
  border-radius: 50px;
  width: 95px;
}

.listing-side-organizer .theme-btn{
  margin-top: 15px;
}


@media all and (min-width: 992px) and (max-width: 1199px) {
  .listing-rating-range-item{
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
  }

  .listing-progress{
    margin: 0px;
  }
}

@media all and (max-width: 991px) {
  .listing-content{
    margin-bottom: 50px;
  }
}

@media all and (max-width: 767px) {

  .listing-header{
    flex-direction: column;
  }

  .listing-review-rating{
    flex-direction: column;
  }

  .listing-rating-range-item{
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
  }

  .listing-progress{
    margin: 0px;
  }

  .listing-review-author-info{
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    margin-left: 15px;
  }

  .review-reply-item{
    margin-left: 30px;
  }

  .listing-flight .flight-booking-time{
    flex-direction: column;
  }

}




/* ===================
27. Deal css 
====================== */

.deal-area{
  position: relative;
}

.deal-item{
  margin-bottom: 25px;
}

.deal-1 .deal-item{
  background: var(--color-white);
  border-radius: 30px;
  padding: 15px 20px;
  box-shadow: var(--box-shadow);
  position: relative;
  z-index: 1;
}

.deal-1 .deal-item::before{
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-image: url(../img/shape/05.png);
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  border-radius: 30px;
  z-index: -1;
}

.deal-1 .deal-info{
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.deal-1 .deal-title{
  font-size: 22px;
}

.deal-1 .deal-price{
  font-weight: 600;
  color: var(--theme-color2);
  font-size: 22px;
}

.deal-1 .deal-item .theme-btn{
  padding: 6px 15px;
  margin-top: 15px;
}

.deal-2 .deal-item{
  padding: 10px;
  border-radius: 50px 50px 50px 0;
  box-shadow: var(--box-shadow);
}

.deal-2 .deal-img{
  overflow: hidden;
  border-radius: 45px 45px 45px 0;
}

.deal-2 .deal-item:hover .deal-img img{
  transform: scale(1.1);
}

.deal-2 .deal-img img{
  border-radius: 45px 45px 45px 0;
}

.deal-2 .deal-info{
  margin: 10px 0 5px 10px;
}

.deal-2 .deal-title{
  font-size: 20px;
}

.deal-2 .deal-title a:hover{
  color: var(--theme-color);
}

.deal-2 .deal-price span{
  font-weight: 600;
  color: var(--theme-color2);
}



/* ===================
28. Download css 
====================== */

.download-area{
  position: relative;
}

.download-img {
  text-align: center;
  position: relative;
  z-index: 1;
}

.download-img::before{
  content: "";
  position: absolute;
  bottom: 0;
  width: 500px;
  height: 500px;
  background: var(--theme-color-light);
  border-radius: 50% 50% 50% 50px;
  z-index: -1;
}

.download-img img{
  width: 63%;
}

.download-feature{
  margin: 20px 0 35px 0;
}

.download-feature li{
  font-weight: 500;
  margin: 15px 0;
  text-transform: capitalize;
}

.download-feature li i{
  width: 30px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  background: var(--theme-color);
  color: var(--color-white);
  border-radius: 50px;
  margin-right: 10px;
}

.download-link a img{
  border-radius: 12px;
  width: 90%;
}

@media all and (min-width: 992px) and (max-width: 1199px) {
  .download-img::before{
    width: 400px;
    height: 400px;
    left: 50%;
    transform: translateX(-50%);
  }
}

@media all and (max-width: 991px) {
  .download-img{
    margin-bottom: 60px;
  }
}

@media all and (max-width: 767px) {
  .download-img::before{
    width: 280px;
    height: 280px;
    left: 50%;
    transform: translateX(-50%);
  }

  .download-link a:first-child{
    margin-bottom: 15px;
  }
}




/* ===================
29. Choose css 
====================== */

.choose-item{
  display: flex;
  gap: 15px;
  background: var(--color-white);
  box-shadow: var(--box-shadow);
  position: relative;
  padding: 20px;
  border-radius: 100px 100px 100px 0;
  margin-bottom: 30px;
  transition: var(--transition);
}

.choose-item:hover{
  transform: translateY(4px);
}

.choose-item .count{
  position: absolute;
  top: -8px;
  left: 10px;
  font-size: 14px;
  font-weight: 600;
  color: var(--color-white);
  background: var(--theme-color2);
  line-height: 1;
  padding: 4px 10px;
  border-radius: 50px;
  box-shadow: var(--box-shadow);
}

.choose-item .icon{
  width: 80px;
  height: 80px;
  line-height: 75px;
  text-align: center;
  background: var(--theme-color);
  border-radius: 50px;
}

.choose-item .icon img{
  width: 50px;
  filter: brightness(0) invert(1);
}

.choose-item .content{
  flex: 1;
}

.choose-item .content h4{
  font-size: 20px;
  margin-bottom: 5px;
}

.choose-img{
  text-align: right;
  position: relative;
}

.choose-img .shape{
  position: absolute;
  top: -15px;
  left: 70px;
  width: 35%;
}

.choose-img .img-1{
  width: 58%;
  border-radius: 200px;
}

.choose-img .img-2{
  position: absolute;
  left: 50px;
  bottom: 0px;
  width: 50%;
  border-radius: 200px;
  border: 10px solid var(--color-white);
}




/* ===================
30. Booking sidebar
=================== */

.booking-sidebar{
  background: var(--color-white);
  border-radius: 25px;
  padding: 20px;
  box-shadow: var(--box-shadow);
}

.booking-sidebar .booking-item{
  margin-bottom: 35px;
}

.booking-sidebar .booking-item:last-child{
  margin-bottom: 0;
}

.booking-sidebar .booking-title{
  font-size: 19px;
  position: relative;
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(0, 0, 0, .05);
}

.booking-sidebar .form-check-input{
  margin-top: 6px;
  box-shadow: none;
  border-color: var(--color-dark);
  border-radius: 6px;
}

.booking-sidebar .form-check-input:focus{
  border-color: var(--theme-color);
}

.booking-sidebar .form-check-label{
  width: 100%;
  color: var(--color-dark);
}

.booking-sidebar .form-check-label span{
  float: right;
}

.booking-sidebar .form-check{
  margin: 12px 0;
}

.booking-sidebar .flight-time .form-check{
  padding: 10px 15px 10px 38px;
  border-radius: 15px;
  background: var(--theme-bg-light);
}

.booking-sidebar .flight-time .form-check-label i{
  border-left: 1px solid rgba(0, 0, 0, .1);
  padding-left: 12px;
  margin-left: 5px;
  margin-right: 5px;
}



/* ===================
31. Booking sort
=================== */
.booking-sort {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--color-white);
  margin-bottom: 25px;
  padding: 10px 10px 10px 15px;
  border-radius: 20px;
  box-shadow: var(--box-shadow);
}

.booking-sort-list-grid a {
  width: 35px;
  height: 35px;
  line-height: 35px;
  border-radius: 10px;
  text-align: center;
  border: 1px solid var(--border-info-color);
  color: var(--color-dark);
  font-size: 18px;
}

.booking-sort-list-grid a.active {
  border-color: var(--theme-color);
  color: var(--theme-color);
}

.booking-sort .nice-select {
  width: 100%;
  height: 46px;
  line-height: 44px;
  border-radius: 14px;
  padding-left: 15px;
  font-size: 16px;
  border-color: var(--border-info-color);
  color: var(--color-dark);
}

.booking-sort .form-control:focus,
.booking-sort .nice-select:focus {
  border-color: var(--theme-color);
}

.booking-sort .nice-select .list {
  width: 100%;
  border: none;
  border-radius: 12px;
  padding: 10px;
  box-shadow: 0px 0px 50px 0px rgb(32 32 32 / 15%);
}

.booking-sort .nice-select .option.focus,
.booking-sort .nice-select .option.selected.focus,
.booking-sort .nice-select .option:hover {
  background: #F6F6F6;
  color: var(--theme-color);
  border-radius: 8px;
}

.booking-sort .nice-select:after {
  width: 8px;
  height: 8px;
  margin-top: -6px;
  right: 15px;
}

@media all and (max-width: 767px) {
  .booking-sort {
    flex-direction: column;
    gap: 10px;
    padding-top: 20px;
    padding-bottom: 20px;
  }

  .booking-sort-box{
    width: 100%;
  }

}



/* ===================
32. Flight booking css 
====================== */

.flight-booking-item{
  background: var(--color-white);
  border-radius: 25px;
  margin-bottom: 25px;
  padding: 20px 20px 15px 20px; 
  box-shadow: var(--box-shadow);
}

.flight-booking-wrapper{
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flight-booking-item .flight-booking-content{
  display: flex;
  align-items: center;
  gap: 50px;
}

.flight-booking-item .flight-booking-airline{
  display: flex;
  align-items: center;
  gap: 15px;
}

.flight-booking-item .flight-airline-img{
  width: 90px;
  padding: 15px;
  border-radius: 20px;
  text-align: center;
  border: 1px solid var(--border-info-color);
}

.flight-booking-item .flight-booking-time .start-time,
.flight-booking-item .flight-booking-time .end-time{
  display: flex;
  align-items: center;
  gap: 12px;
}

.flight-booking-item .flight-booking-time .start-time-text,
.flight-booking-item .flight-booking-time .end-time-text{
  color: var(--color-dark);
  font-weight: 700;
}

.flight-booking-item .flight-destination{
  color: var(--color-dark);
  font-weight: 500;
}

.flight-booking-item .flight-booking-time{
  display: flex;
  align-items: center;
  gap: 30px;
}

.flight-booking-item .start-time-icon{
  font-size: 25px;
  color: var(--color-dark);
}

.flight-booking-item .flight-stop{
  text-align: center;
}

.flight-booking-item .flight-stop-number{
  color: var(--color-dark);
  font-weight: 500;
}

.flight-booking-item .flight-stop-arrow{
  margin-top: 5px;
  border-top: 2px solid var(--color-dark);
  position: relative;
  width: 140px;
}

.flight-booking-item .flight-stop-arrow::before{
  content: "";
  position: absolute;
  border-style: solid;
  border-width: 10px 10px 0 0;
  border-color: var(--color-dark) transparent transparent transparent;
  right: -1.5px;
  top: -9.6px;
  transform: scaleY(-1);
}

.flight-booking-item .flight-has-stop::after{
  content: "\e122";
  position: absolute;
  left: 50%;
  top: -15px;
  font-family: "Font Awesome 6 Pro";
  color:var(--color-dark);
  font-weight: bold;
}

.flight-booking-item .flight-booking-return .flight-has-stop::after{
  top: -16px;
}

.flight-booking-item .flight-booking-duration .duration-text{
  color: var(--color-dark);
  font-weight: 500;
}

.flight-booking-item .price-info{
  margin-bottom: 15px;
}

.flight-booking-item .price-info .price-amount{
  color: var(--theme-color2);
  font-weight: 600;
  font-size: 20px;
}

.flight-booking-item .price-info .discount-price{
  margin-right: 5px;
  color: var(--color-dark);
}

.flight-booking-item .flight-booking-price{
  padding-left: 30px;
  text-align: right;
  border-left: 1px solid rgba(0, 0, 0, .05);
}

.flight-booking-item .flight-booking-price .theme-btn{
  padding: 7px 18px;
}

.flight-booking-item .flight-booking-return{
  margin-top: 20px;
}

.flight-booking-item .flight-booking-return .flight-stop-arrow::before{
  right: unset;
  left: -1.5px;
  top: -9.6px;
  border-width: 0 0 10px 10px;
  border-color: transparent transparent  var(--color-dark) transparent;
  transform: scaleY(1);
}

.flight-booking-detail{
  margin-top: 20px;
  padding-top: 10px;
  border-top: 1px solid rgba(0, 0, 0, .05);
}

.flight-booking-detail-header{
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flight-booking-detail-header p{
  color: var(--color-dark);
}

.flight-booking-detail-header a{
  color: var(--color-dark);
  font-weight: 500;
}

.flight-booking-detail-wrapper{
  margin-top: 12px;
  padding-top: 15px;
  border-top: 1px solid var(--border-info-color);
}

.flight-booking-detail-wrapper .nav-tabs{
  border-color: var(--border-info-color);
}

.flight-booking-detail-wrapper .nav-tabs .nav-item .nav-link{
  font-weight: 500;
  color: var(--color-dark);
  border: none;
  border-bottom: 2px solid transparent;
}

.flight-booking-detail-wrapper .nav-tabs .nav-link.active{
  border-color: var(--theme-color);
  color: var(--theme-color);
}

.flight-booking-detail-left .flight-booking-airline{
  margin-top: 15px;
  margin-bottom: 35px;
}

.flight-booking-detail-left .flight-airline-model{
  color: var(--color-dark);
  font-weight: 500;
  font-size: 14px;
}

.flight-booking-detail-left .flight-airline-class{
  color: var(--color-dark);
  font-weight: 500;
}

.flight-booking-detail-left .flight-full-date{
  font-size: 14px;
  color: var(--color-dark);
  font-weight: 500;
}

.flight-booking-detail-left .flight-booking-time{
  gap: unset;
  justify-content: space-between;
}

.flight-booking-detail-left .flight-stop-arrow{
  width: 100px;
}

.flight-booking-detail-right{
  position: relative;
  height: 100%;
  padding-bottom: 65px;
}

.flight-booking-detail-info{
  padding-top: 15px;
}

.flight-booking-detail-info .table{
  color: var(--color-dark);
}

.flight-booking-detail-info .table th,
.flight-booking-detail-info .table td{
  color: var(--color-dark);
}

.flight-booking-detail-price{
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: absolute;
  width: 100%;
  background: var(--theme-bg-light);
  border-radius: 0 0 5px 0;
  left: 0;
  bottom: 0;
  padding: 8px 18px;
  border-radius: 15px;
}

.flight-detail-price-amount{
  color: var(--theme-color2);
  font-weight: 600;
  font-size: 18px;
}

.flight-booking-policy ul li{
  font-size: 14px;
  margin: 6.1px 0;
  color: var(--color-dark);
}


/* flight booking grid */

.flight-grid .flight-booking-wrapper{
  flex-direction: column;
}

.flight-grid .flight-booking-item .flight-booking-content{
  justify-content: space-between;
}

.flight-grid .flight-booking-info{
  width: 100%;
}

.flight-grid .flight-booking-item .flight-booking-price{
  border-left: 0;
  padding-left: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  text-align: unset;
  width: 100%;
  margin-top: 30px;
}

.flight-grid .flight-booking-item .price-info{
  margin-bottom: 0;
}

.flight-grid .flight-booking-airline{
  flex-direction: column;
}

.flight-grid .flight-booking-detail-left .flight-booking-airline{
  flex-direction: row;
}

.flight-grid .flight-booking-item .flight-airline-img{
  width: 140px;
}

.flight-grid .flight-booking-detail-info .flight-airline-img {
  width: 80px;
}

.flight-grid .flight-booking-item .flight-airline-img img{
  width: 100%;
}

.flight-grid .flight-booking-item .flight-booking-content {
  flex-direction: column;
  gap: 20px;
}

.flight-grid .flight-booking-item .flight-booking-return{
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid var(--border-info-color);
}

.flight-grid .flight-booking-detail-left .flight-booking-detail-info{
  padding-bottom: 30px;
}



@media all and (max-width: 1199px) {

  .flight-booking-wrapper{
    flex-direction: column;
  }

  .flight-booking-item .flight-booking-content{
    justify-content: space-between;
  }

  .flight-booking-info{
    width: 100%;
  }

  .flight-booking-item .flight-booking-price{
    border-left: 0;
    padding-left: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    text-align: unset;
    width: 100%;
    margin-top: 30px;
  }

  .flight-booking-item .price-info{
    margin-bottom: 0;
  }

 .flight-booking-detail-left .flight-booking-detail-info{
    padding-bottom: 30px;
  }

}


@media all and (max-width: 767px) {

  .flight-booking-item .flight-airline-img{
    width: 140px;
  }

  .flight-booking-detail-info .flight-booking-airline{
    flex-direction: row;
  }

  .flight-booking-detail-info .flight-airline-img {
    width: 80px;
  }

  .flight-booking-item .flight-airline-img img{
    width: 100%;
  }

  .flight-booking-item .flight-booking-content {
    flex-direction: column;
    gap: 20px;
  }

  .flight-booking-item .flight-booking-time{
    gap: 15px;
  }

  .flight-booking-item .flight-stop-number{
    font-size: 13px;
  }

  .flight-booking-item .flight-stop-arrow{
    width: 100px;
  }

  .flight-booking-item .start-time-icon{
    font-size: 20px;
  }

  .flight-booking-item .flight-booking-time .start-time-text,
  .flight-booking-item .flight-booking-time .end-time-text{
    font-size: 15px;
  }

  .flight-booking-item .flight-destination{
    font-size: 12px;
  }

  .flight-booking-item .flight-booking-return{
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid rgba(0, 0, 0, .05);
  }

}




/* ===================
33. Cart css 
====================== */

.cart-area thead tr {
  background: var(--theme-color);
  color: var(--color-white);
}

.cart-area thead tr th {
  text-transform: uppercase;
  font-weight: 500;
  color: var(--color-dark);
}

.cart-area thead tr th,
.cart-area thead tr td {
  white-space: nowrap;
  vertical-align: middle;
}

.cart-area tr th,
.cart-area tr td {
  vertical-align: middle;
}

.cart-product{
  display: flex;
  align-items: center;
  gap: 20px;
}

.cart-product-content{
  flex: 1;
  white-space: nowrap;
}

.cart-product-title {
  font-size: 18px;
  font-weight: 500;
  color: var(--color-dark);
}

.cart-product-title:hover{
  color: var(--theme-color);
}

.cart-img {
  width: 120px;
  height: 120px;
}

.cart-img img {
  width: 100%;
  border-radius: 30px 30px 30px 0;
}

.cart-product-info span span{
  color: var(--color-dark);
  font-weight: 500;
}

.cart-area tr td {
  color: var(--color-dark);
  vertical-align: middle;
  border-bottom: 1px solid var(--border-info-color);
  border-top: none;
  position: relative;
  padding: 20px 10px;
  font-size: 16px;
}

.cart-qty {
  width: 150px;
}

.cart-qty button {
  color: var(--color-dark);
  border: 0;
  border-radius: 12px;
  padding: 4px 13px;
  background: var(--theme-bg-light);
}

.cart-qty input {
  width: 50px;
  padding: 4px 13px;
  border-radius: 12px;
  border: none;
  background: var(--theme-bg-light);
  color: var(--color-dark);
  text-align: center;
}

.cart-remove{
  width: 45px;
  height: 45px;
  font-size: 20px;
  text-align: center;
}

.cart-remove:hover {
  color: var(--theme-color2);
}

.cart-footer {
  margin-top: 40px;
}

.cart-coupon {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  margin-bottom: 30px;
  position: relative;
}

.cart-coupon .form-control {
  box-shadow: none;
  padding: 15px 15px;
  border-radius: 18px;
  border-color: var(--border-info-color);
  color: var(--color-dark);
}

.cart-coupon .form-control::placeholder{
  color: var(--body-text-color);
}

.cart-coupon .form-control:focus {
  border-color: var(--theme-color2);
}

.coupon-btn {
  position: absolute;
  right: 6px;
  top: 6px;
  padding: 8px 14px;
  color: var(--color-white);
  border: none;
  background: var(--theme-color2);
  border-radius: 15px;
  transition: var(--transition);
}

.coupon-btn:hover {
  background: var(--theme-color);
}

.cart-summary li {
  margin-bottom: 10px;
  width: 100%;
}

.cart-summary li span {
  float: right;
}

.cart-summary li strong {
  color: var(--color-dark);
}

.cart-total {
  padding-top: 10px;
  border-top: 1px solid var(--border-info-color);
}

.cart-total span {
  font-weight: bold;
  color: #FE4B7B;
}



/* ===================
34. Booking css 
====================== */

.booking-widget {
  background: var(--color-white);
  padding: 30px;
  border-radius: 25px;
  margin-bottom: 30px;
  box-shadow: var(--box-shadow);
}

.booking-widget-title {
  margin-bottom: 20px;
}

.booking-form .form-group {
  margin-bottom: 20px;
}

.booking-form .form-group label {
  margin-bottom: 5px;
  color: var(--color-dark);
  font-size: 15px;
  font-weight: 500;
}

.booking-form .form-group .form-control {
  padding: 15px 18px 15px 45px;
  border-radius: 15px;
  box-shadow: none;
  color: var(--color-dark);
  border-color: var(--border-info-color);
}

.booking-form .form-group .form-control::placeholder{
  color: var(--body-text-color);
}

.booking-form .form-group .nice-select{
  width: 100%;
  height: 56px;
  line-height: 55px;
  padding-left: 45px;
  border-radius: 15px;
  font-size: 16px;
  color: var(--color-dark);
  border-color: var(--border-info-color);
}

.booking-form .form-group .nice-select:focus,
.booking-form .form-group .form-control:focus {
  border-color: var(--theme-color);
}

.booking-form .form-group .nice-select:after{
  width: 9px;
  height: 9px;
  right: 20px;
  margin-top: -7px
}

.booking-form .form-group .nice-select .list{
  width: 100%;
  border: none;
  max-height: 250px;
  overflow-y: auto;
  border-radius: 12px;
  box-shadow: var(--box-shadow);
}

.booking-form .form-group-icon{
  position: relative;
}

.booking-form .form-group-icon i{
  position: absolute;
  left: 18px;
  top: 20px;
  color: var(--theme-color);
}

.booking-summary {
  padding: 20px;
  border-radius: 25px;
  box-shadow: var(--box-shadow);
}

.booking-property-img img {
  border-radius: 50px 50px 50px 0;
}

.booking-property-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  margin-bottom: 10px;
}

.booking-property-title h5 {
  margin-bottom: 5px;
  font-size: 20px;
}

.booking-property-title p {
  margin-bottom: 0px;
  font-size: 15px;
}

.booking-property-title p i {
  color: var(--theme-color);
  margin-right: 5px;
}

.book-edit-btn {
  width: 32px;
  height: 32px;
  line-height: 30px;
  text-align: center;
  background: var(--theme-color);
  border-radius: 12px;
  color: var(--color-white);
}

.book-edit-btn:hover {
  background: var(--theme-color2);
  color: var(--color-white);
}

.booking-property-rate {
  font-size: 15px;
}

.booking-property-rate .badge{
  background: #FFA903;
  margin-right: 5px;
  border-radius: 50px 50px 50px 0;
}

.rate-text {
  color: #646a9b;
}

.rate-type {
  color: var(--theme-color);
}

.booking-info-summary{
  margin-top: 25px;
}

.booking-info-summary h5{
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border-info-color);
}

.booking-info-summary li{
  font-weight: 500;
  color: var(--color-dark);
  margin-bottom: 12px;
}

.booking-info-summary li span{
  float: right;
  font-weight: 400;
  color: var(--body-text-color);
}

.booking-order-info h5{
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border-info-color);
}

.booking-order-info {
  margin-top: 30px;
}

.booking-order-info span {
  float: right;
  font-weight: 400;
  color: var(--body-text-color);
}

.booking-order-info li {
  margin-bottom: 12px;
  font-weight: 500;
  color: var(--color-dark);
}

.order-total {
  padding-top: 10px;
  border-top: 1px solid var(--border-info-color);
}

.order-total span {
  font-weight: bold;
  color: var(--theme-color2);
} 

.booking-payment-area .nav-link{
  background: transparent !important;
  border: 1px solid var(--border-info-color);
  text-align: center;
  border-radius: 15px;
  margin-right: 15px;
  margin-bottom: 20px;
  position: relative;
}

.booking-payment-area .nav-link::before{
  content: "\f058";
  position: absolute;
  font-family: "Font Awesome 6 Pro";
  font-weight: bold;
  font-size: 20px;
  color: var(--theme-color);
  transform: scale(0);
  opacity: 0;
  visibility: hidden;
  left: 7px;
  top: 0;
  transition: all .3s ease-in-out;
}

.booking-payment-area .nav-link.active::before{
  opacity: 1;
  visibility: visible;
  transform: scale(1);
}

.booking-payment-area .nav-link span{
  color: var(--color-dark);
}

.payment-card-img{
  padding-top: 15px;
  margin-bottom: 13px;
}

.payment-card-img img{
  width: 30px;
}

.booking-payment-img img{
  width: 130px;
}

.booking-payment-area .nav-link.active{
  border-color: var(--theme-color);
}



/* ===================
35. Booking confirm css 
====================== */

.booking-confirm-content {
  padding: 50px;
  text-align: center;
  border-radius: 15px;
  box-shadow: var(--box-shadow);
}

.booking-confirm-icon i {
  width: 70px;
  height: 70px;
  line-height: 70px;
  background: var(--theme-color);
  color: var(--color-white);
  font-size: 35px;
  border-radius: 50px;
  text-align: center;
}

.booking-confirm-content h3 {
  margin-top: 20px;
}

.booking-confirm-content p {
  margin: 15px 0;
}

.booking-confirm-content .theme-btn {
  margin-top: 15px;
}

.booking-confirm .booking-summary {
  padding: 50px;
  text-align: center;
  border-radius: 15px;
  box-shadow: var(--box-shadow);
}

.booking-confirm .booking-summary-content {
  padding: 80px 0;
}

.booking-confirm .booking-summary-list {
  text-align: left;
}

.booking-confirm .booking-summary-list .text-success{
  color: var(--theme-color) !important;
}

.booking-confirm .booking-summary-list h6 {
  padding-bottom: 15px;
  margin-bottom: 15px;
  border-bottom: 1px solid rgba(0, 0, 0, .1);
}

.booking-confirm .booking-summary-list li span {
  float: right;
}

.cancel-booking-note p{
  font-weight: 500;
}

.booking-confirm .theme-btn.cancel-booking{
  background: var(--theme-color2);
  margin-left: 15px;
}




/* ===================
36. About css 
====================== */

.about-left{
  position: relative;
  margin-right: 40px;
}

.about-left::before{
  content: "";
  position: absolute;
  width: 50%;
  height: 80%;
  border-radius: 200px;
  border: 10px double var(--theme-color);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: -1;
}

.about-img img{
  border-radius: 200px;
}

.about-img .img-2{
  margin-top: 90px;
}

.about-right {
  position: relative;
  display: block;
}

.about-experience{
  background: var(--theme-color);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 150px;
  height: 150px;
  line-height: 1.2;
  border-radius: 100px;
  text-align: center;
  border: 10px solid var(--color-white);
}

.about-experience h5{
  color: var(--color-white);
  font-size: 40px;
  margin-top: 15px;
}

.about-experience p{
  color: var(--color-white);
  font-weight: 600;
}

.about-content {
  margin-top: 25px;
  margin-bottom: 35px;
}

.about-item{
  display: flex;
  gap: 12px;
  border-radius: 25px;
  padding: 15px;
  position: relative;
  box-shadow: var(--box-shadow);
}

.about-item::before{
  content: "";
  position: absolute;
  left: 0;
  top: 30px;
  bottom: 30px;
  border-left: 2px solid var(--theme-color);
}

.about-item .icon{
  width: 68px;
  height: 68px;
  line-height: 65px;
  text-align: center;
  background: var(--theme-color);
  border-radius: 50px;
}

.about-item .icon img{
  width: 45px;
  filter: brightness(0) invert(1);
}

.about-item .content{
  flex: 1;
}

.about-item .content h6{
  color: var(--color-dark);
  font-size: 18px;
  margin-bottom: 5px;
}

.about-text{
  color: var(--body-text-color);
}


@media all and (max-width: 991px) {
  .about-right {
    margin-top: 50px;
  }
}

@media all and (max-width: 767px) {
  .about-left{
    margin-right: 0;
  }

  .about-title {
    font-size: 30px;
  }

  .about-img .img-2{
    margin-top: 60px;
    margin-left: -10px;
  }

  .about-img .img-3{
    margin-left: -30px;
  }

  .about-experience{
    right: -7px;
    bottom: -60px;
  }
}





/* ===================
37. Service css 
====================== */

.service-area {
  position: relative;
  display: block;
  overflow: hidden;
}

.service-item {
  position: relative;
  padding: 25px;
  margin-bottom: 25px;
  border-radius: 100px 100px 100px 0;
  background: var(--color-white);
  text-align: center;
  transition: var(--transition);
  box-shadow: var(--box-shadow);
  z-index: 1;
}

.service-content {
  position: relative;
}

.service-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100px;
  height: 100px;
  background: var(--theme-color);
  border-radius: 50px 50px 50px 0;
  left: 50%;
  transform: translateX(-50%);
  margin-bottom: 12px;
  position: relative;
  transition: var(--transition);
}

.service-icon i {
  color: var(--color-white);
  font-size: 40px;
  line-height: 1;
}

.service-icon img {
  width: 60px;
  filter: brightness(0) invert(1);
}

.service-arrow {
  margin-top: 15px;
}

.service-title a {
  font-size: 23px;
  font-weight: 600;
  margin-bottom: 10px;
  color: var(--color-dark);
}

.service-title a:hover {
  color: var(--theme-color);
}

.service-arrow a {
  width: 50px;
  height: 50px;
  line-height: 43px;
  border-radius: 50px;
  border: 3px solid var(--theme-color);
  color: var(--theme-color);
  font-size: 20px;
  text-align: center;
  position: relative;
  transition: var(--transition);
  z-index: 1;
}

.service-item:hover .service-arrow a {
  background: var(--theme-color);
}

.service-item .service-arrow a i {
  transition: var(--transition);
}

.service-item:hover .service-arrow a i {
  color: var(--color-white);
}



/* ====================
38. Service single
==================== */

.service-single-wrapper .widget-title {
  font-weight: 600;
}

.service-single-list i {
  color: var(--theme-color);
  margin-right: 10px;
}

.service-download a {
  border: 2px solid var(--theme-color);
  padding: 10px 20px;
  color: var(--color-dark);
  display: block;
  margin-bottom: 20px;
  font-weight: 600;
  border-radius: 20px;
  transition: var(--transition);
}

.service-download a i {
  margin-right: 10px;
}

.service-download a:hover {
  background-color: var(--theme-color);
  color: var(--color-white);
}

.service-details h3 {
  color: var(--color-dark);
  font-weight: 600;
}

.service-details img {
  border-radius: 25px;
}




/* ===================
39. Feature css 
====================== */

.feature-area {
  position: relative;
}

.feature-img img{
  border-radius: 100px;
}

.feature-item {
  display: flex;
  gap: 15px;
  padding: 20px;
  position: relative;
  border-radius: 30px;
  transition: var(--transition);
  box-shadow: var(--box-shadow);
}

.feature-content{
  flex: 1;
}

.feature-icon {
  width: 70px;
  height: 70px;
  line-height: 65px;
  background: var(--theme-color);
  text-align: center;
  font-size: 40px;
  color: var(--color-white);
  border-radius: 50px 50px 50px 0;
}

.feature-icon img{
  width: 45px;
  filter: brightness(0) invert(1);
}

.feature-title {
  color: var(--color-dark);
  margin-bottom: 10px;
}

/* .feature-area2 */
.feature-area2 .feature-item{
  align-items: center;
  background: var(--theme-color);
  border-radius: 100px;
  padding-right: 35px;
}

.feature-area2 .feature-icon{
  border-radius: 50px;
  background: var(--color-white);
}

.feature-area2 .feature-icon img{
  filter: unset;
}

.feature-area2 .feature-content{
  color: var(--color-white);
}

.feature-area2 .feature-content h4{
  color: var(--color-white);
}

@media all and (max-width: 991px) {
  .feature-item {
    margin-bottom: 25px;
  }
}



/*====================
40. Video css 
======================*/

.video-content {
  position: relative;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  border-radius: 200px 0 0 200px;
}

.video-wrapper {
  position: relative;
  display: flex;
  justify-content: center;
  height: 400px;
  z-index: 100;
}

.video-wrapper img {
  border-radius: 12px;
}

.video-area .play-btn {
  display: inline-block;
  padding: 0;
  height: 75px;
  width: 75px;
  text-align: center;
  position: absolute;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}




/* ===================
41. Video css 
====================== */

.process-single {
  position: relative;
}

.process-single .icon {
  height: 90px;
  width: 90px;
  line-height: 80px;
  display: inline-block;
  position: relative;
  border-radius: 50px 50px 50px 0;
  font-size: 37px;
  color: var(--color-white);
  background: var(--theme-color);
  padding: 5px;
  margin-bottom: 30px;
  box-shadow: 0 3px 24px rgb(0 0 0 / 10%);
}

.process-single .icon::before {
  content: "";
  position: absolute;
  left: -10px;
  right: -10px;
  top: -10px;
  bottom: -10px;
  border-radius: 50%;
  border: 2px dashed var(--theme-color);
}

.process-single .icon img {
  border-radius: 50%;
}

.process-single span {
  position: absolute;
  height: 35px;
  width: 35px;
  font-size: 14px;
  background: var(--theme-color2);
  line-height: 35px;
  color: var(--color-white);
  left: -8px;
  top: -13px;
  font-weight: 600;
  border-radius: 0 50px 50px 50px;
}

.process-single h4 {
  margin-bottom: 10px;
  font-weight: 700;
}

.process-single::before {
  content: "";
  position: absolute;
  width: 150px;
  border-top: 2px dashed rgba(0, 0, 0, .15);
  right: -170px;
  top: 50px;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .process-single::before {
    display: none;
  }
}

@media (max-width: 767px) {
  .process-single::before {
    display: none;
  }
}

.process-area .col-lg-3:last-child .process-single::before {
  display: none;
}



/* ===================
42. Testimonial css 
====================== */

.testimonial-area {
  position: relative;
}

.testimonial-area.ts-bg{
  position: relative;
  background-image: url(../img/testimonial/bg.jpg);
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  background-attachment: fixed;
  z-index: 1;
}

.testimonial-area.ts-bg::before{
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(13, 44, 122, .65);
  z-index: -1;
}

.testimonial-area .shadow-text{
  position: absolute;
  left: 50%;
  bottom: -10px;
  font-size: 250px;
  line-height: 250px;
  font-weight: 800;
  text-transform: uppercase;
  color: var(--color-white);
  font-family: var(--heading-font);
  letter-spacing: 40px;
  transform: translateX(-50%);
  -webkit-text-fill-color: transparent;
  -webkit-text-stroke: 2px var(--color-white);
  opacity: .3;
}

.testimonial-single {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-top: 30px;
  margin-bottom: 20px;
  background: var(--color-white);
  border-radius: 200px;
  padding: 20px 40px 20px 20px;
  position: relative;
  z-index: 1;
}

.testimonial-single .count{
  position: absolute;
  right: 100px;
  bottom: 10px;
  font-size: 120px;
  line-height: 120px;
  font-weight: 800;
  color: var(--theme-color);
  font-family: var(--heading-font);
  -webkit-text-fill-color: transparent;
  -webkit-text-stroke: 3px var(--theme-color);
  opacity: .1;
  z-index: -1;
}

.testimonial-quote p {
  color: var(--color-dark);
}

.testimonial-author-img {
  position: relative;
  width: 220px;
  height: 220px;
  padding: 15px;
}

.testimonial-author-img::before{
  content: "";
  position: absolute;
  left: 0px;
  top: 0px;
  width: 100%;
  height: 100%;
  border: 4px solid var(--theme-color);
  border-top-color: var(--color-white);
  border-right-color: var(--color-white);
  border-radius: 200px;
  transition: var(--transition);
}

.testimonial-single:hover .testimonial-author-img::before{
  transform: rotate(45deg);
}

.testimonial-author-img img {
  border-radius: 50%;
}

.testimonial-author-info h4 {
  font-size: 22px;
  color: var(--color-dark);
}

.testimonial-author-info p {
  color: var(--theme-color);
  font-weight: 500;
}

.testimonial-quote-icon {
  position: absolute;
  right: 80px;
  top: 5px;
  width: 80px;
}

.testimonial-rate {
  color: var(--theme-color);
  margin-top: 10px;
}

.testimonial-slider.owl-theme .owl-nav {
  margin-top: 0px;
}

.testimonial-slider.owl-theme .owl-nav button{
  color: var(--theme-color);
  font-size: 25px;
  margin: 0;
  margin-top: -10px;
  padding: 0;
  background: var(--color-white);
  display: inline-block;
  cursor: pointer;
  height: 45px;
  width: 45px;
  border-radius: 50px;
  line-height: 45px;
  text-align: center;
  box-shadow: 0 3px 24px rgb(0 0 0 / 10%);
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
  transition: var(--transition);
}

.testimonial-slider.owl-theme .owl-nav button:hover{
  background: var(--theme-color);
  color: var(--color-white);
}

.testimonial-slider.owl-theme .owl-nav .owl-prev {
  left: -25px;
}

.testimonial-slider.owl-theme .owl-nav .owl-next {
  right: -25px;
}

.testimonial-area .owl-dots {
  text-align: center;
}

.testimonial-area .owl-dots .owl-dot span {
  background: transparent;
  margin: 5px;
  border: 2px solid var(--theme-color);
  border-radius: 50px;
  width: 12px;
  height: 12px;
  display: inline-block;
  transition: var(--transition);
}

.testimonial-area .owl-dots .owl-dot.active span {
  background: var(--theme-color);
}

@media all and (max-width: 991px) {
  .testimonial-single{
    flex-direction: column;
    text-align: center;
  }

  .testimonial-area .shadow-text{
    font-size: 130px;
    line-height: 130px;
  }

  .testimonial-quote-icon{
    right: 40px;
  }
}

@media all and (max-width: 767px) {
  .testimonial-slider.owl-theme .owl-nav {
    display: none;
  }

  .testimonial-area .shadow-text{
    font-size: 80px;
    line-height: 80px;
    letter-spacing: 15px;
  }
}




/* ===================
43. Counter css 
====================== */

.counter-negative{
  margin-top: -90px;
}

.counter-wrap {
  position: relative;
  padding: 50px 20px;
  position: relative;
  background: var(--theme-color);
  border-radius: 0 80px 0 0;
  z-index: 1;
}

.counter-wrap::before{
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-image: url(../img/shape/01.png);
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  border-radius: 0 80px 0 0;
  z-index: -1;
}

.counter-box {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  gap: 15px;
  position: relative;
  z-index: 1;
}

.counter-box .icon {
  position: relative;
  font-size: 35px;
  width: 80px;
  height: 80px;
  line-height: 75px;
  text-align: center;
  background: var(--color-white);
  border-radius: 30px;
  color: var(--theme-color);
}

.counter-box .icon img {
  width: 50px;
}

.counter-box .counter-number{
  display: flex;
  align-items: center;
}

.counter-box .counter-sign{
  color: var(--color-dark);
  font-size: 40px;
  line-height: 0;
  margin-left: 2px;
}

.counter-box .counter {
  display: block;
  line-height: 1;
  color: var(--color-dark);
  font-size: 50px;
  font-weight: 600;
}

.counter-box .title {
  color: var(--color-dark);
  margin-top: 10px;
  font-size: 20px;
  font-weight: 600;
  text-transform: capitalize;
}

@media all and (max-width: 991px) {
  .counter-area .counter-box {
    margin: 40px 0;
    justify-content: flex-start;
  }
}



/* ===================
44. Cta-area css 
====================== */

.cta-area {
  position: relative;
}

.cta-img img{
  border-radius: 200px;
}

.cta-content{
  position: relative;
  background: var(--color-white);
  border-radius: 30px;
  box-shadow: var(--box-shadow);
  padding: 20px 30px;
  text-align: center;
  margin-bottom: -150px;
  z-index: 1;
}

.cta-content::before{
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-image: url(../img/shape/05.png);
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  border-radius: 30px;
  opacity: .6;
  z-index: -1;
}

.cta-content .cta-text h1 {
  color: var(--color-dark);
  font-size: 35px;
  font-weight: 600;
  text-transform: capitalize;
  margin-bottom: 15px;
}

.cta-content .cta-text span {
  color: var(--theme-color);
}

.cta-content .cta-text p {
  color: var(--body-text-color);
}

@media all and (max-width: 767px) {
  .cta-content{
    margin-bottom: -50px;
  }

  .cta-img img{
    width: 100%;
    height: 200px;
    object-fit: cover;
  }
}




/* ========================
45. Gallery css 
=========================== */

.gallery-item {
  position: relative;
  width: 100%;
}

.gallery-img {
  height: 100%;
}

.gallery-img img {
  width: 100%;
  border-radius: 50px 50px 50px 0;
}

.gallery-content {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}

.gallery-content::before {
  content: "";
  position: absolute;
  left: 10px;
  top: 10px;
  right: 10px;
  bottom: 10px;
  background: var(--theme-color);
  border-radius: 50px 50px 50px 0;
  opacity: 0;
  visibility: hidden;
  transition: 0.3s;
}

.gallery-content:hover::before {
  opacity: 0.9;
  visibility: visible
}

.gallery-link {
  width: 50px;
  height: 50px;
  background: var(--color-white);
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  transition: 0.5s;
  border-radius: 50px;
  opacity: 0;
  visibility: hidden;
  font-size: 25px;
  color: var(--theme-color);
  margin-top: 50px;
}

.gallery-link::before {
  content: "";
  position: absolute;
  left: -5px;
  right: -5px;
  top: -5px;
  bottom: -5px;
  border: 1px dashed var(--color-white);
  border-radius: 50px;
}

.gallery-link:hover {
  color: var(--theme-color);
}

.gallery-content:hover .gallery-link {
  opacity: 1;
  visibility: visible;
  margin-top: 0px;
}



/* ===================
46. Mission css 
====================== */

.mission-img{
  position: relative;
}

.mission-left{
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 1;
}

.mission-img img{
  border-radius: 50%;
  box-shadow: 0 0 30px 20px rgb(0 0 0 / 8%);
}

.mission-img .mission-img-1 {
  width: 380px;
}

.mission-img .mission-img-2 {
  position: absolute;
  bottom: -50px;
  right: -40px;
  border-radius: 100%;
  width: 190px;
  height: 190px;
  border: 8px solid var(--color-white);
}

.mission-shape{
  position: absolute;
  right: 90px;
  top: 0px;
  z-index: -1;
}


@media all and (max-width: 991px) {
  .mission-right{
    margin-top: 100px;
  }
}

@media all and (max-width: 767px) {
  .mission-shape{
    right: 0;
  }

  .mission-img .mission-img-2 {
    bottom: -70px;
    right: -10px;
  }
}




/* ===================
47. Career css 
====================== */

.career-item{
  padding: 20px;
  background: var(--color-white);
  border-radius: 50px 50px 50px 0;
  box-shadow: var(--box-shadow);
}

.job-meta{
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.job-meta .badge{
  background: var(--theme-color2);
  border-radius: 50px 50px 50px 0;
  padding: 6px 12px;
}

.job-time{
  color: var(--theme-color);
}

.job-time i{
  margin-right: 5px;
}

.job-info-list{
  display: flex;
  justify-content: start;
  align-items: center;
  gap: 15px;
  color: var(--color-dark);
}

.job-info-list li span{
  margin-right: 8px;
  color: var(--theme-color);
}

.job-content h3{
  font-size: 25px;
  margin: 10px 0px;
  color: var(--color-dark);
}

.job-btn{
  margin-top: 30px;
  border: 2px solid var(--color-dark);
  color: var(--color-dark);
  padding: 6px 25px;
  border-radius: 50px 50px 50px 0;
  font-weight: bold;
  transition: var(--transition);
}

.job-btn:hover{
  background: var(--color-dark);
  color: var(--color-white);
}

.job-btn i{
  margin-left: 5px;
}

.career-promo-single {
  text-align: center;
  background: var(--color-white);
  padding: 20px 35px;
  border-radius: 50px 50px 50px 0;
  box-shadow: var(--box-shadow);
}

.career-promo-single span{
  width: 80px;
  height: 80px;
  line-height: 80px;
  background: var(--theme-color);
  border-radius: 50px 50px 50px 0;
  color: var(--color-white);
  font-size: 35px;
  margin-bottom: 20px;
}

.career-promo-single .career-promo-info h4{
  margin-bottom: 14px;
}


@media all and (max-width: 767px) {
  .job-meta{
    flex-direction: column-reverse;
    align-items: flex-start;
  }

  .job-info-list{
    flex-direction: column;
    align-items: flex-start;
  }

  .job-time{
    margin-top: 20px;
  }
}



/* ===================
48. Career single css 
====================== */
.job-single-img img{
  border-radius: 25px;
  margin-bottom: 10px;
}

.job-single-list li{
  margin-bottom: 14px;
}

.job-single-list li i{
  margin-right: 5px;
  color: var(--theme-color);
  font-size: 12px;
}

.job-overview-list li {
  position: relative;
  display: flex;
  font-size: 16px;
  margin-bottom: 20px;
}

.job-overview-list li i {
  margin-right: 15px;
  font-size: 22px;
  color: var(--theme-color);
  margin-top: 6px;
}

.overview-item h6{
  font-size: 18px;
}

@media all and (max-width: 991px) {
  .job-single-info{
    margin-top: 40px;
  }
}




/* ===================
49. Blog css 
====================== */

.blog-item {
  margin-bottom: 25px;
  background: var(--color-white);
  padding: 20px;
  border-radius: 100px 100px 100px 0;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
  position: relative;
}

.blog-date{
  position: absolute;
  left: 100px;
  top: 2px;
  background: var(--color-white);
  color: var(--theme-color);
  font-weight: 500;
  border-radius: 50px;
  padding: 2px 15px;
  z-index: 1;
}

.blog-item-img {
  overflow: hidden;
  border-radius: 85px 85px 85px 0;
}

.blog-item-img img {
  border-radius: 85px 85px 85px 0;
}

.blog-item:hover .blog-item-img img {
  transform: scale(1.1);
}

.blog-item-meta ul {
  padding: 10px 0;
  margin-bottom: 15px;
  border-bottom: 1px solid var(--border-info-color);
}

.blog-item-meta ul li {
  display: inline-block;
  margin-right: 15px;
  font-weight: 500;
  position: relative;
  color: var(--color-dark);
}

.blog-item-meta ul li i {
  margin-right: 5px;
  color: var(--theme-color);
}

.blog-item-meta a:hover {
  color: var(--theme-color);
}

.blog-title {
  font-size: 22px;
  margin-bottom: 15px;
  text-transform: capitalize;
}

.blog-item-info p {
  margin-bottom: 16px;
}

.blog-item-info h4 a {
  color: var(--color-dark);
}

.blog-item-info h4 a:hover {
  color: var(--theme-color);
}




/* ===========================
50. Blog single css
=========================== */

.blog-thumb-img {
  margin-bottom: 20px;
}

.blog-single-content img {
  border-radius: 25px;
}

.blog-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.blog-meta .blog-meta-left ul {
  display: flex;
  align-items: center;
  gap: 20px;
}

.blog-meta .blog-meta-left ul li {
  font-weight: 500;
}

.blog-meta i {
  margin-right: 5px;
  color: var(--theme-color);
}

.blog-meta a {
  color: #66788a;
  font-weight: 500;
}

.blog-meta a:hover {
  color: var(--theme-color);
}

.blog-details-title {
  font-size: 34px;
  color: var(--color-dark);
}

.blockqoute {
  background: var(--theme-bg-light);
  border-left: 5px solid var(--theme-color);
  padding: 30px;
  font-size: 17px;
  font-style: italic;
  margin: 20px 0;
  border-radius: 0px;
}

.blockqoute-author {
  margin-top: 20px;
  padding-left: 60px;
  position: relative;
  color: var(--color-dark);
}

.blockqoute-author::before {
  content: "";
  position: absolute;
  height: 2px;
  width: 40px;
  background: var(--theme-color);
  left: 0;
  top: 10px;
}

.blog-details-tags {
  display: flex;
  align-items: center;
  gap: 20px;
}

.blog-details-tags h5 {
  color: var(--color-dark);
  font-weight: 700;
}

.blog-details-tags ul {
  display: flex;
  align-items: center;
  gap: 15px;
}

.blog-details-tags ul a {
  background: var(--theme-bg-light);
  color: var(--color-dark);
  padding: 6px 18px;
  border-radius: 15px;
  transition: var(--transition);
}

.blog-details-tags ul a:hover {
  background: var(--theme-color);
  color: var(--color-white);
}

.blog-author {
  display: flex;
  justify-content: start;
  align-items: center;
  background: var(--theme-bg-light);
  border-radius: 50px 50px 50px 0;
  margin: 50px 0;
  padding: 20px;
}

.blog-author-img {
  width: 320px;
  position: relative;
}

.blog-author-img::before {
  content: "";
  position: absolute;
  left: -10px;
  right: -10px;
  top: -10px;
  bottom: -10px;
  border-radius: 50%;
}

.blog-author-img img {
  border-radius: 40px 40px 40px 0;
}

.author-name {
  font-size: 22px;
  color: var(--theme-color);
  margin: 8px 0;
}

.author-info {
  padding: 0 20px 0 30px;
}

.author-social {
  margin-top: 10px;
}

.author-social a {
  width: 35px;
  height: 35px;
  line-height: 32px;
  text-align: center;
  border: 2px solid var(--theme-color);
  border-radius: 50px;
  margin-right: 5px;
  color: var(--theme-color);
  transition: var(--transition);
}

.author-social a:hover {
  color: var(--color-white);
  background: var(--theme-color);
}

.blog-comments {
  margin-bottom: 50px;
}

.blog-comments h3 {
  color: var(--color-dark);
  font-weight: 600;
}

.blog-comments-wrapper {
  margin: 30px 0;
}

.blog-comments-single {
  display: flex;
  justify-content: start;
  align-items: flex-start;
  margin-top: 50px;
}

.blog-comments-img {
  margin-top: 3px;
}

.blog-comments-single img {
  border-radius: 50%;
  width: 120px;
}

.blog-comments-content {
  padding: 0 0 0 20px;
}

.blog-comments-content span {
  font-size: 14px;
  color: var(--theme-color);
  font-weight: 500;
}

.blog-comments-content a {
  font-weight: 600;
  margin-top: 5px;
  color: var(--theme-color);
}

.blog-comments-content a:hover {
  color: var(--color-dark);
}

.blog-comments-content h5 {
  color: var(--color-dark);
  font-weight: 700;
  font-size: 20px;
}

.blog-comments-reply {
  margin-left: 50px;
}

.blog-comments-form {
  padding: 30px;
  margin-top: 50px;
  border-radius: 25px;
  background: var(--theme-bg-light);
}

.blog-comments-form h3 {
  margin-bottom: 20px;
}

.blog-comments-form .form-group {
  margin-bottom: 20px;
}

.blog-comments-form .form-control {
  padding: 15px 20px;
  border-radius: 15px;
  box-shadow: none;
  transition: var(--transition);
}

.blog-comments-form .form-control:focus {
  border-color: var(--theme-color);
}


@media all and (max-width: 767px) {
  .blog-meta {
    flex-direction: column;
    font-size: 15px;
  }

  .blog-meta .blog-meta-left ul {
    gap: 10px;
  }

  .blog-details-tags {
    flex-direction: column;
    align-items: flex-start;
  }

  .blog-author {
    flex-direction: column;
    text-align: center;
    padding: 25px 0 0 0;
  }

  .blog-comments-single {
    flex-direction: column;
    text-align: center;
    padding: 30px 0px;
    box-shadow: var(--box-shadow);
    margin-bottom: 30px;
    border-radius: 10px;
  }

  .blog-comments-img {
    width: 100%;
  }

  .blog-comments-content {
    padding: 20px;
  }

  .blog-comments-reply {
    margin-left: 0px;
  }
}


/* =========================
sidebar css
========================= */

.widget {
  background: var(--color-white);
  padding: 30px;
  margin-bottom: 30px;
  border-radius: 25px;
  box-shadow: var(--box-shadow);
}

.widget .widget-title {
  padding-bottom: 10px;
  margin-bottom: 30px;
  position: relative;
  font-size: 23px;
  color: var(--color-dark);
}

.widget .widget-title::before {
  position: absolute;
  content: '';
  width: 15px;
  border-bottom: 3px solid var(--theme-color);
  bottom: 0;
  left: 0;
}

.widget .widget-title::after {
  position: absolute;
  content: '';
  width: 30px;
  border-bottom: 3px solid var(--theme-color);
  bottom: 0;
  left: 22px;
}

.widget .blog-search-form .form-control {
  padding: 12px 15px 12px 15px;
  border-radius: 15px;
  box-shadow: none;
  border-color: var(--border-info-color);
}

.widget .blog-search-form {
  position: relative;
}

.widget .blog-search-form .form-control:focus {
  border-color: var(--theme-color);
}

.widget .blog-search-form button {
  position: absolute;
  right: 0;
  top: 0;
  font-size: 18px;
  padding: 8px 18px 6px 18px;
  background: transparent;
  border: none;
  color: var(--theme-color);
}

.widget .category-list a {
  display: block;
  padding: 10px 0;
  font-weight: 500;
  border-bottom: 1px solid var(--border-info-color);
  transition: var(--transition);
}

.widget .category-list a:last-child {
  margin-bottom: 0px;
  border-bottom: none;
}

.widget .category-list a:hover {
  padding-left: 10px;
  color: var(--theme-color);
}

.widget .category-list a i {
  margin-right: 5px;
  color: var(--theme-color);
}

.widget .category-list a span {
  float: right;
}

.widget .recent-post-single {
  display: flex;
  justify-content: start;
  align-items: center;
  margin-bottom: 20px;
}

.widget .recent-post-img {
  margin-right: 20px;
}

.widget .recent-post-img img {
  width: 120px;
  border-radius: 30px 30px 30px 0;
}

.widget .recent-post-bio h6 {
  font-size: 18px;
}

.widget .recent-post-bio span {
  font-size: 14px;
  color: var(--theme-color);
  font-weight: 500;
}

.widget .recent-post-bio span i {
  margin-right: 5px;
}

.widget .recent-post-bio h6 a:hover {
  color: var(--theme-color);
}

.widget .social-share-link a {
  width: 35px;
  height: 35px;
  line-height: 33px;
  border: 2px solid var(--theme-color);
  color: var(--theme-color);
  text-align: center;
  margin-right: 5px;
  border-radius: 50px;
  transition: var(--transition);
}

.widget .social-share-link a:hover {
  background: var(--theme-color);
  color: var(--color-white);
}

.widget .tag-list a {
  background: var(--theme-bg-light);
  color: var(--color-dark);
  padding: 5px 15px;
  margin-bottom: 10px;
  margin-right: 10px;
  border-radius: 15px;
  display: inline-block;
  transition: var(--transition);
}

.widget .tag-list a:hover {
  background-color: var(--theme-color);
  color: var(--color-white);
}




/* =========================
51. Partner css
========================= */

.partner-negative{
  margin-bottom: -60px;
}

.partner-wrap{
  background: var(--color-white);
  position: relative;
  border-radius: 0 100px 100px 0;
  padding: 20px 20px;
  z-index: 2;
}

.partner-wrap img {
  background: var(--color-white);
  border-radius: 100px;
  padding: 18px 15px;
  margin-top: 5px;
  margin-bottom: 5px;
  border: 1px solid var(--border-info-color);
}



/* ===================
52. Contact us css 
====================== */

.contact-wrapper {
  position: relative;
}

.contact-form {
  background: var(--color-white);
  border-radius: 15px;
  padding: 30px 30px;
  box-shadow: var(--box-shadow);
}

.contact-form-header {
  margin-bottom: 30px;
}

.contact-form-header h2 {
  font-size: 30px;
  margin-bottom: 10px;
  font-weight: 600;
  color: var(--color-dark);
}

.contact-form .form-group {
  margin-bottom: 25px;
}

.contact-form .form-group .form-control {
  padding: 15px 18px;
  border-radius: 15px;
  box-shadow: none;
  transition: var(--transition);
  border-color: var(--border-info-color);
  color: var(--color-dark);
}

.contact-form .form-group .form-control::placeholder{
  color: var(--body-text-color);
}

.contact-form .form-group .form-control:focus {
  border-color: var(--theme-color);
}

.contact-map {
  margin-bottom: -9px;
}

.contact-map iframe {
  width: 100%;
  height: 450px;
}

.contact-content {
  margin-bottom: 50px;
  background: var(--color-white);
  border-radius: 15px;
  padding: 20px 30px;
  position: relative;
  box-shadow: var(--box-shadow);
}

.contact-content::before {
  content: "";
  position: absolute;
  left: 0;
  top: 30px;
  bottom: 30px;
  width: 3px;
  background: var(--theme-color);
  border-radius: 50px;
}

.contact-info {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px 0;
}

.contact-info-icon i {
  font-size: 24px;
  color: var(--color-white);
  width: 60px;
  height: 60px;
  line-height: 60px;
  text-align: center;
  border-radius: 50px 50px 50px 0;
  background: var(--theme-color);
}

.contact-info h5 {
  font-size: 22px;
  margin-bottom: 5px;
  color: var(--color-dark);
}

.contact-info p {
  font-weight: 500;
}


@media all and (max-width: 768px) {
  .contact-content {
    margin-bottom: 50px;
  }
}



/* ===================
53. Team css 
====================== */

.team-area {
  position: relative;
}

.team-item {
  margin-bottom: 70px;
  position: relative;
  z-index: 1;
}

.team-img {
  border-radius: 50%;
}

.team-img img {
  border-radius: 50%;
}

.team-content {
  position: absolute;
  left: 20px;
  right: 20px;
  bottom: -45px;
  padding: 10px 20px 10px 30px;
  background: var(--color-white);
  border-radius: 50px;
  box-shadow: var(--box-shadow);
}

.team-bio h5 {
  font-size: 22px;
  margin-top: 5px;
  margin-bottom: 5px;
  text-transform: capitalize;
}

.team-bio h5 a {
  color: var(--color-dark);
}

.team-bio span {
  color: var(--theme-color);
  font-size: 16px;
  font-weight: 500;
  text-transform: capitalize;
}

.team-bio h5 a:hover {
  color: var(--theme-color);
}

.team-social-btn {
  position: absolute;
  right: 60px;
  top: 65px;
}

.team-social-btn li {
  display: inline-block;
  position: absolute;
  bottom: 0;
  transition: .6s;
  list-style-type: none;
}

.team-social-btn li i {
  display: inline-block;
  width: 45px;
  height: 45px;
  line-height: 45px;
  background: var(--theme-color);
  border-radius: 50px;
  color: var(--color-white);
  text-align: center;
  font-size: 20px;
  transition: .6s;
  opacity: 0;
  visibility: hidden;
  box-shadow: var(--box-shadow);
}

.team-social-btn li i:hover {
  background: var(--color-dark);
  color: var(--color-white);
}

.team-item li span i {
  opacity: 1;
  visibility: visible;
}

.team-item:hover li span i {
  background: var(--color-dark);
  cursor: pointer;
}

.team-item:hover .team-social-btn li i {
  opacity: 1;
  visibility: visible;
}

.team-social-btn li:nth-child(1) {
  z-index: 1;
}

.team-social-btn li:nth-child(2) {
  bottom: 0;
}

.team-social-btn li:nth-child(3) {
  bottom: 0;
}

.team-social-btn li:nth-child(4) {
  bottom: 0;
}

.team-social-btn li:nth-child(5) {
  bottom: 0;
}

.team-item:hover li:nth-child(1) {
  z-index: 1;
}

.team-item:hover li:nth-child(2) {
  bottom: 48px;
}

.team-item:hover li:nth-child(3) {
  bottom: 96px;
}

.team-item:hover li:nth-child(4) {
  bottom: 144px;
}

.team-item:hover li:nth-child(5) {
  bottom: 192px;
}

.team-item:hover li:nth-child(6) {
  bottom: 240px;
}




/* ===================
54. Pricing css 
====================== */

.pricing-item {
  position: relative;
  background: var(--color-white);
  overflow: hidden;
  padding: 30px;
  margin-bottom: 25px;
  border-radius: 100px 100px 100px 0;
  box-shadow: var(--box-shadow);
  transition: all .9s ease-in-out;
}

.pricing-item:hover {
  transform: translateY(-10px);
}

.pricing-content {
  background: var(--theme-color);
  padding: 20px 20px 20px 40px;
  border-radius: 85px 85px 85px 0;
}

.pricing-content h4 {
  margin-bottom: 10px;
  color: var(--color-white);
}

.pricing-amount {
  color: var(--color-dark);
  font-weight: 700;
}

.pricing-content p{
  color: var(--color-dark);
  font-weight: 500;
}

.pricing-feature {
  margin-top: 20px;
}

.pricing-feature ul {
  margin-top: 35px;
  margin-bottom: 35px;
}

.pricing-feature ul li {
  position: relative;
  margin: 10px 0;
  padding-left: 25px;
}

.pricing-feature ul li::before {
  content: "";
  position: absolute;
  width: 13px;
  height: 13px;
  border: 3px solid var(--theme-color);
  border-radius: 50px;
  left: 0;
  top: 8px;
}



/* ===================
55. Faq css 
====================== */

.faq-area .accordion-item {
  border: none;
  margin-bottom: 25px;
  background: var(--color-white);
  color: var(--body-text-color);
  border-radius: 20px !important;
  box-shadow: var(--box-shadow);
}

.faq-area .accordion-item span {
  width: 45px;
  height: 45px;
  margin-right: 15px;
}

.faq-area .accordion-item i {
  width: 45px;
  height: 45px;
  line-height: 45px;
  border-radius: 50px;
  background: var(--theme-color);
  text-align: center;
  color: var(--color-white);
}

.faq-area .accordion-button:not(.collapsed) {
  color: var(--theme-color);
  background: transparent;
  box-shadow: inset 0 -1px 0 rgb(0 0 0 / 13%);
}

.accordion-button {
  border-radius: 0px !important;
  background: transparent;
  font-weight: 600;
  font-size: 20px;
  color: var(--color-dark);
  box-shadow: none !important;
}

.accordion-button:not(.collapsed) {
  border-bottom: 1px dashed var(--theme-color);
}

.accordion-button:not(.collapsed)::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23212529'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}

@media all and (max-width: 991px) {
  .faq-right {
    margin-top: 50px;
  }

  .accordion-button {
    font-size: 16px;
  }
}



/* ===================
56. Breadcrumb css
====================== */

.site-breadcrumb {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  background-repeat: no-repeat !important;
  background-position: center !important;
  background-size: cover !important;
  position: relative;
  padding-top: 270px;
  padding-bottom: 150px;
  z-index: 1;
}

.site-breadcrumb::before {
  content: "";
  position: absolute;
  background: rgba(0, 0, 0, .65);
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  z-index: -1;
}

.site-breadcrumb .breadcrumb-title {
  font-size: 35px;
  color: var(--color-white);
  font-weight: 600;
  margin-bottom: 10px;
  text-transform: capitalize;
}

.site-breadcrumb .breadcrumb-menu {
  position: relative;
  z-index: 1;
}

.site-breadcrumb .breadcrumb-menu li {
  position: relative;
  display: inline-block;
  margin-left: 25px;
  color: var(--color-white);
  font-weight: 500;
  text-transform: capitalize;
}

.site-breadcrumb .breadcrumb-menu li a {
  color: var(--color-white);
  transition: all 0.5s ease-in-out;
}

.site-breadcrumb .breadcrumb-menu li::before {
  position: absolute;
  content: '\e122';
  font-family: 'Font Awesome 6 Pro';
  right: -18px;
  top: 5px;
  text-align: center;
  font-size: 12px;
  color: var(--color-white);
  font-weight: bold;
}

.site-breadcrumb .breadcrumb-menu li:first-child {
  margin-left: 0;
}

.site-breadcrumb .breadcrumb-menu li:last-child:before {
  display: none;
}

.site-breadcrumb .breadcrumb-menu li a:hover {
  color: var(--theme-color);
}

.site-breadcrumb .breadcrumb-menu li.active {
  color: var(--theme-color);
}

@media (max-width: 991px) {
  .site-breadcrumb {
    padding-top: 360px;
  }
}




/* ===================
57. Pagination css 
====================== */

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 40px;
}

.pagination .page-link {
  border: none;
  background: var(--color-dark);
  color: var(--color-white);
  margin: 0 10px;
  border-radius: 15px !important;
  width: 40px;
  height: 40px;
  line-height: 28px;
  text-align: center;
  transition: var(--transition);
}

.pagination .page-link:hover,
.pagination .page-item.active .page-link {
  background: var(--theme-color);
  color: var(--color-white);
}

.pagination-showing{
  text-align: center;
  margin-top: 10px;
  color: var(--color-dark);
}




/* ===================
58. Auth css 
====================== */

.login-form {
  padding: 30px 40px;
  background: var(--color-white);
  border-radius: 50px 50px 50px 0;
  position: relative;
  box-shadow: var(--box-shadow);
}

.login-form .login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-form .login-header img {
  width: 200px;
  margin-bottom: 10px;
}

.login-form .login-header h3 {
  color: var(--theme-color);
  margin-bottom: 5px;
  font-weight: 800;
}

.login-form .login-header p {
  font-size: 20px;
}

.login-form .login-footer {
  margin-top: 25px;
}

.login-form .login-footer p {
  text-align: center;
}

.login-form .login-footer a {
  color: var(--theme-color);
  transition: .5s;
}

.login-form .login-footer a:hover {
  color: var(--theme-color);
}

.login-form .form-group {
  margin-bottom: 15px;
}

.login-form .form-group-icon{
  position: relative;
}

.login-form .form-group i {
  position: absolute;
  top: 16px;
  left: 18px;
  color: var(--theme-color);
}

.login-form label {
  color: var(--color-dark);
  margin-bottom: 5px;
}

.login-form .form-group .form-control {
  color: var(--color-dark);
  border: 1px solid var(--border-info-color);
  border-radius: 15px;
  padding: 12px 18px 12px 47px;
  box-shadow: none;
  transition: .5s;
}

.login-form .form-group .form-control::placeholder{
  color: var(--body-text-color);
}

.login-form .form-group .form-control:focus {
  border-color: var(--theme-color);
}

.login-form .form-check-input {
  box-shadow: none;
  border-radius: 6px;
}

.login-form .forgot-pass {
  color: var(--theme-color);
  transition: .5s;
}

.login-form .forgot-pass:hover {
  color: var(--theme-color);
}

.login-form .theme-btn {
  width: 100%;
}

.login-form .theme-btn::before {
  width: 480px;
  height: 480px;
}

.login-form .theme-btn i {
  margin-right: 5px;
}

.login-form .form-check-input {
  margin-top: 6.3px;
}

.login-form .form-check-label a {
  color: var(--theme-color);
  transition: .5s;
}

.login-form .form-check-label a:hover {
  color: var(--theme-color);
}

.form-check-input:checked {
  background-color: var(--theme-color);
  border-color: var(--theme-color);
}

.login-divider {
  position: relative;
  margin: 15px 0;
  text-align: center;
}

.login-divider::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  border-bottom: 1px solid var(--border-info-color);
}

.login-divider span {
  position: relative;
  background: var(--color-white);
  color: var(--body-text-color);
  padding: 0 20px;
}

.social-login a {
  display: block;
  margin-bottom: 15px;
  padding: 10px 20px;
  border-radius: 20px;
  text-align: center;
}

.social-login a i {
  float: left;
  margin-top: 6px;
}

.btn-fb {
  border: 1px solid #506DAB;
  color: #506DAB !important;
}

.btn-fb:hover {
  background: #506DAB;
  color: var(--color-white) !important;
}

.btn-gl {
  border: 1px solid #DD4B39;
  color: #DD4B39 !important;
}

.btn-gl:hover {
  background: #DD4B39;
  color: var(--color-white) !important;
}


@media only screen and (max-width: 991px) {
  .login-form {
    padding: 40px 20px;
  }
}



/* ===================
59. User profile css 
====================== */

.header-account img {
  width: 50px;
  border-radius: 50%;
  padding: 4px;
  border: 2px solid var(--theme-color);
  cursor: pointer;
}

.header-account .dropdown-menu {
  border: none;
  min-width: 200px;
  border-radius: 10px;
  box-shadow: var(--box-shadow);
}

.header-account .dropdown-menu li a i {
  margin-right: 5px;
  color: var(--theme-color);
}

.header-account .dropdown-menu .dropdown-item {
  padding: 8px 20px;
  color: var(--color-dark);
  font-weight: 500;
}

.header-account .dropdown-menu .dropdown-item:hover {
  background: transparent;
  color: var(--theme-color);
  padding-left: 25px;
}

.mobile-menu-right {
  gap: 20px;
}

.mobile-menu-right .header-account img {
  width: 30px;
  padding: 0;
  border: none;
}

.user-profile-sidebar {
  background: var(--color-white);
  border-radius: 25px;
  margin-bottom: 25px;
  padding: 20px;
  box-shadow: var(--box-shadow);
}

.user-profile-sidebar-top {
  text-align: center;
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(0, 0, 0, .1);
}

.user-profile-img {
  position: relative;
  width: 100px;
  height: 100px;
  margin: 0 auto 15px auto;
  border-radius: 50px;
}

.user-profile-img img {
  border-radius: 50%;
}

.user-profile-sidebar-top p{
  color: var(--color-dark);
}

.profile-img-file {
  display: none;
}

.user-profile-img button {
  position: absolute;
  border-radius: 50px;
  background: var(--theme-color);
  color: var(--color-white);
  width: 30px;
  height: 30px;
  line-height: 28px;
  text-align: center;
  border: none;
  right: 0;
  bottom: 0;
}

.user-profile-sidebar-list li {
  margin: 5px 0;
}

.user-profile-sidebar-list li a {
  color: var(--color-dark);
  padding: 6px 6px 6px 15px;
  font-weight: 500;
  display: block;
  transition: var(--transition);
}

.user-profile-sidebar-list li a:hover {
  color: var(--theme-color);
}

.user-profile-sidebar-list li a i {
  margin-right: 5px;
  color: var(--theme-color);
}

.user-profile-sidebar-list .active i {
  color: var(--color-white);
}

.user-profile-sidebar-list .active {
  background: var(--theme-color);
  color: var(--color-white) !important;
  border-radius: 50px 50px 50px 0;
}

.user-profile-sidebar-list li .badge{
  float: right;
  margin-top: 5px;
  background: var(--theme-color2);
  border-radius: 50px;
}

.profile-menu-angle{
  float: right;
  margin-top: 6px;
}

.profile-menu-list{
  margin-left: 40px;
}

.profile-menu-list li a{
  position: relative;
}

.profile-menu-list li a::before{
  content: "\e122";
  position: absolute;
  font-family: "Font Awesome 6 Pro";
  left: 0px;
  top: 13px;
  color: var(--theme-color);
  font-size: 8px;
  font-weight: bold;
}

.user-profile-card {
  background: var(--color-white);
  padding: 15px 25px;
  border-radius: 25px;
  margin-bottom: 25px;
  box-shadow: var(--box-shadow);
}

.user-profile-card-title {
  color: var(--color-dark);
  padding-bottom: 15px;
  margin-bottom: 20px;
  border-bottom: 1px solid rgba(0, 0, 0, .1);
}

.user-profile-card .text-success {
  color: var(--theme-color) !important;
}

.user-profile-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 15px;
  margin-bottom: 15px;
  border-bottom: 1px solid rgba(0, 0, 0, .1);
}

.user-profile-card-header .user-profile-card-title {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.user-profile-card-header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.user-profile-search .form-group {
  position: relative;
}

.user-profile-search .form-control {
  padding: 10px 12px 10px 35px;
  box-shadow: none;
  border-radius: 15px;
  border-color: var(--border-info-color);
  color: var(--color-dark);
}

.user-profile-search .form-control::placeholder{
  color: var(--body-text-color);
}

.user-profile-search .form-control:focus {
  border-color: var(--theme-color);
}

.user-profile-search i {
  position: absolute;
  left: 12px;
  top: 16px;
}

.user-profile-card-header .theme-btn {
  padding: 8px 15px;
}

.profile-info-list li {
  margin: 12px 0;
  font-weight: bold;
  color: var(--color-dark);
}

.profile-info-list li span {
  float: right;
  font-weight: normal;
}

.user-profile-form .form-group {
  margin-bottom: 20px;
}

.user-profile-form .form-group label {
  color: var(--color-dark);
  margin-bottom: 5px;
}

.user-profile-form .form-control {
  padding: 14px 20px;
  border-radius: 15px;
  box-shadow: none;
  border-color: var(--border-info-color);
  color: var(--color-dark);
}

.user-profile-form .form-control::placeholder{
  color: var(--body-text-color);
}

.user-profile-form .form-control:focus {
  border-color: var(--theme-color);
}

.user-profile-form .theme-btn {
  width: unset;
}


/* dashboard */
.dashboard-widget {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30px 20px;
  border-radius: 25px;
  margin-bottom: 30px;
  box-shadow: 0 0 40px 5px rgb(0 0 0 / 3%);
}

.dashboard-widget-info span {
  font-weight: 500;
}

.dashboard-widget-info h1 {
  font-size: 30px;
  font-weight: 700;
  margin-bottom: 5px;
}

.dashboard-widget-icon {
  width: 65px;
  height: 65px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  border-radius: 50px;
  position: relative;
}

.dashboard-widget-icon::before{
  content: "";
  position: absolute;
  inset: -5px;
  border: 1px dashed var(--color-white);
  border-radius: 50px;
}

.dashboard-widget-color-1 {
  background: #008FFB;
  color: var(--color-white);
}

.dashboard-widget-color-1 h1 {
  color: var(--color-white);
}

.dashboard-widget-color-1 .dashboard-widget-icon {
  background: var(--color-white);
  color: #008FFB;
}

.dashboard-widget-color-2 {
  background: #25BDC7;
  color: var(--color-white);
}

.dashboard-widget-color-2 h1 {
  color: var(--color-white);
}

.dashboard-widget-color-2 .dashboard-widget-icon {
  background: var(--color-white);
  color: #25BDC7;
}

.dashboard-widget-color-3 {
  background: #F96768;
  color: var(--color-white);
}

.dashboard-widget-color-3 h1 {
  color: var(--color-white);
}

.dashboard-widget-color-3 .dashboard-widget-icon {
  background: var(--color-white);
  color: #F96768;
}

.user-notification-item {
  border-bottom: 1px solid rgba(0, 0, 0, .1);
  padding: 10px 0;
}

.user-notification-item a {
  display: flex;
  gap: 15px;
}

.user-notification-item a:hover {
  color: var(--color-dark);
}

.user-notification-item:first-child {
  padding-top: 0;
}

.user-notification-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.user-notification-icon {
  width: 30px;
  height: 30px;
  line-height: 30px;
  border-radius: 50px;
  background: var(--theme-color);
  color: var(--color-white);
  margin-top: 5px;
  text-align: center;
}

.user-notification-info {
  flex: 1;
}

.user-notification-info p {
  font-size: 15px;
}

.user-notification-info span {
  font-size: 14px;
}

.table-property-info a {
  display: flex;
  align-items: center;
  gap: 10px;
}

.table-property-info h6 {
  transition: var(--transition);
}

.table-property-info:hover h6 {
  color: var(--theme-color);
}

.table-property-info img {
  width: 70px;
  border-radius: 8px;
}

.user-profile-card .table>:not(caption)>*>* {
  vertical-align: middle;
  padding: 0.8rem 0.8rem;
}

.user-profile-card .table th,
.user-profile-card .table td{
  color: var(--color-dark);
}

/* profile badge */
.user-profile-card .badge{
  border-radius: 50px;
}

.user-profile-card .badge-success{
  background: rgba(9, 173, 149, .2);
  color: #09AD95;
}

.user-profile-card .badge-warning{
  background: rgba(0, 143, 251, .2);
  color: #008FFB;
}

.user-profile-card .badge-danger{
  background: rgba(249, 103, 104, .2);
  color: #F96768;
}


/* profile btn */
.user-profile-card .btn{
  border-radius: 8px;
}


/* profile table */
.user-profile-card .table{
  color: var(--color-dark);
}


/* my listing */
.table-listing-info a{
  display: flex;
  gap: 15px;
}

.user-profile-listing img {
  width: 120px;
  border-radius: 50px 50px 50px 0;
}

.table-listing-content h6 {
  font-size: 18px;
  font-weight: 600;
}

.table-listing-content p {
  font-size: 15px;
  color: var(--color-dark);
}

.table-listing-content p i{
  color: var(--theme-color);
}

.table-listing-content span {
  font-weight: 700;
  color: var(--color-dark);
}

.user-profile-listing .bg-success {
  background: var(--theme-color) !important;
}

.user-profile-listing .table tbody tr td {
  padding: 1.5rem 0.8rem;
}


/* add listing */
.add-listing-form .form-group {
  margin-bottom: 15px;
}

.add-listing-form .form-group label {
  margin-bottom: 5px;
  color: var(--color-dark);
  font-size: 15px;
}

.add-listing-form .form-control {
  border-radius: 15px;
  padding: 15px 20px;
  box-shadow: none;
  color: var(--color-dark);
  border-color: var(--border-info-color);
}

.add-listing-form .form-control::placeholder {
  color: var(--body-text-color);
}

.add-listing-form .nice-select {
  width: 100%;
  height: 56px;
  line-height: 55px;
  border-radius: 15px;
  padding-left: 20px;
  font-size: 16px;
  border-color: var(--border-info-color);
  color: var(--body-text-color);
  margin-bottom: 15px;
}

.add-listing-form .form-control:focus,
.add-listing-form .nice-select:focus {
  border-color: var(--theme-color);
}

.add-listing-form .nice-select .list {
  width: 100%;
  border: none;
  border-radius: 12px;
  padding: 10px;
  box-shadow: 0px 0px 50px 0px rgb(32 32 32 / 15%);
}

.add-listing-form .nice-select .option.focus,
.add-listing-form .nice-select .option.selected.focus,
.add-listing-form .nice-select .option:hover {
  background: #F6F6F6;
  color: var(--theme-color);
  border-radius: 8px;
}

.add-listing-form .nice-select:after {
  width: 8px;
  height: 8px;
  margin-top: -5px;
  right: 15px;
}

.add-listing-form .listing-img-upload {
  width: 100%;
  height: 140px;
  border: 2px dashed var(--border-info-color);
  border-radius: 15px;
  cursor: pointer;
}

.add-listing-form .listing-img-file {
  display: none;
}

.add-listing-form .listing-img-upload span {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
}

.add-listing-form .listing-img-upload span i {
  margin-right: 5px;
}

.add-listing-form .form-check {
  margin-bottom: 15px;
}

.add-listing-form .form-check-input {
  margin-top: 6.5px;
  border-radius: 6px;
  box-shadow: none;
}

.add-listing-form .form-check-input:focus {
  border-color: var(--theme-color);
}

.add-listing-form .form-check label {
  color: var(--color-dark);
}

.add-listing-form .form-group-icon{
  position: relative;
}

.add-listing-form .form-group-icon i{
  position: absolute;
  left: 18px;
  top: 20px;
}

.add-listing-form .form-group-icon .form-control{
  padding-left: 42px;
}


/* profile message */
.profile-message-wrapper {
  display: flex;
}

.profile-message-inbox {
  max-width: 340px;
  border: 1px solid #EDF1F9;
  border-radius: 10px;
}

.profile-message-inbox {
  overflow: hidden;
}

.message-content {
  padding-left: 30px;
  position: relative;
  overflow: hidden;
}

.message-content-info {
  max-height: 750px;
  overflow-y: scroll;
  margin-right: -50px;
  padding-right: 50px;
}

.profile-message-inbox,
.message-content {
  flex: 1;
}

.profile-message-inbox ul {
  max-height: 950px;
  overflow-y: scroll;
  width: 357px;
  list-style: none;
  padding: 0;
  margin: 0;
}

.profile-message-inbox ul li.message-active {
  border-left: 3px solid var(--theme-color);
}

.profile-message-inbox ul li {
  border-bottom: 1px solid #EDF1F9;
  transition: .2s;
  list-style: none;
}

.profile-message-inbox ul li:last-child {
  border-bottom: none;
}

.message-by-content h5 {
  margin-bottom: 0;
  font-size: 14px;
  line-height: 1;
}

.profile-message-inbox ul li a {
  position: relative;
  display: block;
  padding: 30px;
}

.profile-message-inbox .message-avatar {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
}

.message-avatar img {
  display: inline-block;
  width: 54px;
  height: 54px;
  border-radius: 50%;
}

.message-avatar img {
  width: 50px;
  height: 50px;
}

.profile-message-inbox .message-by {
  margin-left: 50px;
}

.message-by-content h5 i {
  background: #F98F14;
  padding: 3px 8px;
  border-radius: 50px;
  color: var(--color-white);
  font-size: 13px;
}

.message-by-content span {
  font-size: 13px;
  position: absolute;
  top: 25.5px;
  right: 25px;
  float: right;
  color: #888888;
}

.profile-message-inbox .message-by p {
  height: 26px;
  max-width: 205px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  font-size: 14px;
  margin: 0;
  padding: 0;
  color: #6B747D;
  line-height: 27px;
}

.message-item {
  display: block;
  position: relative;
  margin-bottom: 25px;
}

.message-item .message-avatar {
  position: absolute;
  left: 0;
  top: 0;
}

.message-item .message-avatar img {
  width: 50px;
  height: 50px;
  display: inline-block;
  border-radius: 50%;
}

.message-item .message-description {
  margin-left: 70px;
  background: #F4F5F7;
  border-radius: 12px;
  padding: 20px;
  position: relative;
}

.message-item .message-description::before {
  position: absolute;
  top: 0px;
  left: -9px;
  content: "\f0d9";
  font-family: "Font Awesome 6 Pro";
  font-weight: bold;
  font-size: 31px;
  color: #F4F5F7;
}

.message-item .message-description p {
  font-size: 15px;
  padding: 0;
  margin: 0;
  line-height: 27px;
}

.message-item.me .message-avatar {
  left: auto;
  right: 0;
  position: absolute;
  top: 0;
}

.message-item.me .message-description {
  color: #25BDC7;
  background-color: #E9F7F5;
  margin-left: 0;
  margin-right: 70px;
  border-radius: 12px;
  padding: 20px;
  position: relative;
}

.message-item.me .message-description::before {
  content: "\f0da";
  left: auto;
  right: -10px;
  color: #E9F7F5;
}

.message-reply {
  margin-top: 15px;
  position: absolute;
  bottom: 0;
  left: 30px;
  right: 15px;
}

.message-reply .form-control {
  padding: 15px 20px;
  box-shadow: none;
  border-radius: 10px;
}

.message-reply .form-control:focus {
  border-color: var(--theme-color);
}

.message-reply .theme-btn {
  margin-top: 15px;
}

.message-status {
  width: 12px;
  height: 12px;
  background: #252629;
  display: flex;
  border-radius: 50%;
  border: 3px solid var(--color-white);
  position: absolute;
  right: -5px;
  top: 50%;
}

.message-status.online {
  background: #09AD95;
}

.message-status.offline {
  background: #DD4B39;
}

.message-status.busy {
  background: #F6B500;
}

.profile-message .header-account img {
  width: 45px;
}



@media all and (max-width: 991px) {
  .message-by-content span {
    right: 65px;
  }

  .message-reply {
    right: 0;
  }
}

@media all and (max-width: 767px) {
  .table-property-info a {
    display: block;
  }

  .table-property-info h6 {
    margin-top: 10px;
  }

  .user-profile-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
  }

  .user-profile-card-header-right {
    flex-direction: column-reverse;
    align-items: flex-start;
  }

  .profile-message .profile-message-wrapper {
    flex-direction: column;
    gap: 50px;
  }

  .profile-message .user-profile-card-header {
    flex-direction: row;
    align-items: center;
    gap: 20px;
  }

  .profile-message .message-content {
    padding-left: 0;
    padding-bottom: 200px;
  }

  .message-reply {
    left: 0;
  }

}



/* ===================
60. Coming soon css 
====================== */

.coming-soon {
  background-repeat: no-repeat;
  background-size: cover !important;
  background-position: center !important;
  min-height: 100vh;
  width: 100%;
  top: 0;
  left: 0;
  position: relative;
}

.coming-soon .container {
  position: relative;
}

.coming-soon:before {
  position: absolute;
  content: '';
  background: rgba(0, 0, 0, .7);
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}

.coming-soon .newsletter-form {
  position: relative;
  margin-top: 30px;
}

.coming-soon .newsletter-form .input-newsletter {
  height: 55px;
  display: block;
  width: 100%;
  border: none;
  border-radius: 50px;
  font-size: 14px;
  padding-top: 0;
  padding-right: 0;
  padding-bottom: 0;
  padding-left: 25px;
  box-shadow: none;
}

.coming-soon .newsletter-form button {
  position: absolute;
  right: 4px;
  top: 4px;
  bottom: 4px;
  height: auto;
  border: none;
  border-radius: 50px;
  background: var(--theme-color);
  display: inline-block;
  color: var(--color-white);
  padding-left: 30px;
  padding-right: 30px;
  font-weight: 500;
  transition: .5s;
}

.coming-soon .newsletter-form button:hover {
  background-color: var(--theme-color);
  color: var(--color-white);
}

.coming-social {
  margin-top: 30px;
  text-align: center;
}

.coming-social a {
  color: var(--color-white);
  margin: 5px;
  width: 40px;
  height: 40px;
  line-height: 36px;
  text-align: center;
  border-radius: 50px;
  border: 2px solid var(--color-white);
  display: inline-block;
  transition: .5s;
}

.coming-social a:hover {
  background: var(--theme-color);
  border-color: var(--theme-color);
}

.countdown-wrap {
  margin-top: 30px;
}

.countdown-single {
  border: 10px solid var(--theme-color);
  background: var(--color-white);
  padding: 15px 20px;
  margin: 10px;
  position: relative;
  overflow: hidden;
  border-radius: 100px;
  box-shadow: 0 3px 24px rgb(0 0 0 / 10%);
}

.countdown-single h2 {
  color: var(--theme-color);
}

.countdown-single h5 {
  color: var(--theme-color);
}




/* ===================
61. Error css 
====================== */

.error-wrapper {
  text-align: center;
}

.error-wrapper h1 {
  font-size: 250px;
  letter-spacing: 5px;
  font-weight: bold;
  color: var(--theme-color);
  line-height: 1;
}

.error-wrapper h1 span {
  color: var(--color-dark);
}

.error-wrapper h2 {
  margin-top: 30px;
  margin-bottom: 10px;
}

.error-wrapper img {
  width: 100%;
}

.error-wrapper .theme-btn {
  margin-top: 30px;
}


@media all and (max-width: 767px) {
  .error-wrapper h1 {
    font-size: 160px;
  }
}



/* ===================
62. Terms/privacy css 
====================== */

.terms-content:not(:last-child) {
  margin-bottom: 54px;
}

.terms-content:first-child {
  margin-top: -3px;
}

.terms-content .terms-list {
  margin-top: 37px;
}

.terms-content h3 {
  margin-bottom: 23px;
  position: relative;
  font-weight: 600;
}

.terms-content p:not(:last-child) {
  margin-bottom: 26px;
}

.terms-list li:not(:last-child) {
  margin-bottom: 16px;
}




/* ===================
63. Footer css 
====================== */

.footer-area {
  background: var(--footer-bg);
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.footer-area.ft-bg::before{
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-image: url(../img/shape/02.png);
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  opacity: .1;
  z-index: -1;
}

.footer-widget-box {
  margin-bottom: 20px;
}

.footer-widget {
  position: relative;
  z-index: 1;
}

.footer-logo img {
  width: 200px;
  margin-bottom: 30px;
}

.copyright {
  padding: 20px 0;
  border-top: 1px dashed var(--border-white-color);
}

.copyright .footer-menu {
  margin: 0;
  padding: 0;
  text-align: right;
}

.copyright .footer-menu li {
  display: inline-block;
  margin-left: 25px;
  font-size: 16px;
}

.copyright .footer-menu li a {
  color: var(--footer-text-color);
  transition: var(--transition);
}

.copyright .footer-menu li a:hover {
  color: var(--theme-color);
}

.copyright .copyright-text {
  color: var(--footer-text-color);
  margin-bottom: 0px;
  font-size: 16px;
}

.copyright .copyright-text a {
  color: var(--theme-color);
  font-weight: 500;
}

.footer-widget-title {
  color: var(--footer-text-color);
  position: relative;
  padding-bottom: 20px;
  margin-bottom: 30px;
  font-weight: 700;
  font-size: 20px;
  z-index: 1;
}

.footer-widget-title::before {
  position: absolute;
  content: '';
  z-index: -1;
  width: 30px;
  height: 2.5px;
  background-color: var(--theme-color);
  bottom: 0;
  left: 0;
}

.footer-widget-title::after {
  position: absolute;
  content: '';
  z-index: -1;
  width: 10px;
  height: 2.5px;
  background-color: var(--theme-color);
  bottom: 0;
  left: 35px;
}

.footer-list {
  display: flex;
  flex-direction: column;
  gap: .8rem;
}

.footer-list li a {
  color: var(--footer-text-color);
  transition: var(--transition);
}

.footer-list li a i {
  margin-right: 5px;
  color: var(--theme-color);
  font-size: 13px;
}

.footer-list li a:hover {
  padding-left: 10px;
  color: var(--theme-color);
}

.footer-widget-box p {
  color: var(--footer-text-color);
  padding-right: 18px;
  margin-bottom: 20px;
}

.footer-social {
  display: flex;
  gap: 15px;
  justify-content: end;
}

.footer-social li a i {
  height: 38px;
  width: 38px;
  line-height: 38px;
  text-align: center;
  border-radius: 50px;
  background: rgba(255, 255, 255, .08);
  color: var(--theme-color);
  transition: var(--transition);
}

.footer-social li a i:hover {
  background: var(--theme-color);
  color: var(--color-white);
}

.footer-contact li {
  position: relative;
  display: flex;
  justify-content: start;
  align-items: center;
  color: var(--footer-text-color);
  font-size: 16px;
  margin-bottom: 15px;
}

.footer-contact li a {
  color: var(--footer-text-color);
  transition: 0.3s;
}

.footer-contact li i {
  width: 32px;
  height: 32px;
  line-height: 32px;
  font-size: 16px;
  margin-right: 15px;
  border-radius: 50px 50px 50px 0;
  background: var(--theme-color);
  text-align: center;
  transition: 0.3s;
  color: var(--color-white);
}

.footer-call{
  display: flex;
  align-items: center;
  gap: 14px;
  margin-bottom: 10px;
}

.footer-call-icon i{
  background: transparent !important;
  font-size: 45px !important;
  color: var(--theme-color) !important;
}

.footer-call-info h6{
  color: var(--footer-text-color);
  font-weight: 700;
}

.footer-call-info a{
  color: var(--theme-color) !important;
  font-size: 20px;
  font-weight: 700;
}

.subscribe-form .form-control {
  padding: 14px 20px;
  border-radius: 18px;
  box-shadow: none;
  border: none;
  color: var(--color-dark);
}

.subscribe-form .form-control::placeholder{
  color: var(--color-dark);
}

.subscribe-form .form-group-icon{
  position: relative;
}

.subscribe-form .form-group-icon i{
  position: absolute;
  left: 20px;
  top: 19px;
  color: var(--theme-color);
}

.subscribe-form .form-group-icon .form-control{
  padding: 15px 18px 15px 45px;
}

.subscribe-form .theme-btn {
  margin-top: 20px;
  width: 100%;
}

.subscribe-form p{
  margin-top: 5px;
  margin-bottom: 10px;
}

.footer-payment-method{
  display: flex;
  align-items: center;
  margin-top: 15px;
  gap: 8px;
}

.footer-payment-method h6{
  font-weight: 700;
  color: var(--footer-text-color);
  white-space: nowrap;
}

.payment-method-img img{
  width: 35px;
  margin: 2px;
  border-radius: 4px;
}

@media all and (min-width: 1199px) and (max-width: 1399px) {
  .payment-method-img img{
    width: 33px;
  }
}

@media all and (max-width: 1199px) {
  .footer-widget-box {
    margin-bottom: 50px;
  }
}

@media all and (max-width: 991px) {
  .footer-widget-wrapper {
    padding-bottom: 0px;
  }

  .copyright .footer-menu {
    float: left;
    margin-top: 20px;
    text-align: left;
  }

  .copyright .footer-menu li {
    margin-left: 0;
    margin-right: 15px;
  }

}

@media all and (max-width: 767px) {
  .footer-widget-wrapper {
    padding-bottom: 0px;
  }

  .footer-social {
    justify-content: flex-start;
    margin-top: 20px;
  }
}




/* ====================
64. Home 2
==================== */

.home-2 .main{
  margin-top: 0rem;
}

.home-2 .header-top{
  background: var(--theme-color);
  padding: 2px 0 3px 0;
} 

.home-2 .header-top::before{
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: -1px;
  width: 62%;
  background: var(--color-dark);
  border-radius: 0 50px 0 0;
  z-index: -1;
}

.home-2 .navbar{
  background: var(--color-white);
}

.home-2 .navbar-toggler-btn-icon{
  color: var(--color-dark);
}

.home-2 .hero-single{
  padding-top: 200px;
  padding-bottom: 220px;
  border-radius: 40px;
}

.home-2 .hero-single::before{
  border-radius: 40px;
  background: rgba(0, 0, 0, .3);
}

.home-2 .header-top-right .account a:hover {
  color: var(--color-white);
}

.home-2 .search-wrapper{
  margin-top: -50px;
}

.home-2 .footer-area{
  --footer-bg: #0D233E;
}

@media all and (min-width: 992px){
  .home-2 .navbar .nav-item .nav-link {
    color: var(--color-dark);
  }

  .home-2 .navbar .nav-item .nav-link.active,
  .home-2 .navbar .nav-item .nav-link:hover {
    color: var(--theme-color);
  }
}



/* ====================
65. Home 3
==================== */

.home-3 .main{
  margin-top: -6rem;
}

.home-3 .hero-single{
  padding-top: 320px;
  padding-bottom: 180px;
}

.home-3 .hero-single::before{
  background: rgba(0, 0, 0, .5);
}

.home-3 .search-wrapper{
  margin-top: 0;
}

.home-3 .search-header{
  justify-content: center;
  flex-direction: unset;
  margin: unset;
}

.home-3 .search-nav{
  box-shadow: var(--box-shadow);
}

.home-3 .search-wrapper .tab-content{
  padding: 45px 25px 45px 25px;
}

.home-3 .search-form .search-btn .theme-btn{
  right: unset !important;
  top: unset;
  left: 50%;
  bottom: -20px;
  transform: translateX(-50%);
  box-shadow: var(--box-shadow);
}


/* ====================
66. Home 4
==================== */

.home-4 .header-top{
  border-bottom: 1px solid var(--border-info-color);
}

.home-4 .top-social a{
  border-color: var(--theme-color);
  color: var(--theme-color);
}

.home-4 .top-social a:hover{
  background: var(--theme-color);
  color: var(--color-white);
}

.home-4 .top-contact-info ul li a{
  color: var(--color-dark);
}

.home-4 .header-top-right .account a{
  color: var(--color-dark);
}

.home-4 .header-top-right .nice-select{
  color: var(--color-dark);
}

.home-4 .header-top-right .nice-select:after{
  border-color: var(--color-dark);
}

.home-4 .hero-single{
  padding-top: 240px;
  padding-bottom: 0;
}

.home-4 .hero-single::before{
  background: transparent;
  background-image: url(../img/shape/06.png);
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  opacity: .3;
}

.home-4 .hero-single .hero-content .hero-title{
  color: var(--color-dark);
}

.home-4 .hero-single .hero-content p{
  color: var(--color-dark);
}

.home-4 .hero-single .hero-img{
  margin-top: 50px;
}

.home-4 .hero-single .hero-img img{
  border-radius: 200px;
}

.home-4 .search-wrapper{
  margin-top: -70px;
}

.home-4 .search-form .flight-type{
  margin-bottom: 15px;
}

.home-4 .search-form .form-group{
  border-radius: 20px;
  background: transparent;
  border: 1px solid rgba(37, 189, 199, .4);
}

@media all and (min-width: 992px){
  .home-4 .navbar .nav-item .nav-link {
    color: var(--color-dark);
  }

  .home-4 .navbar .nav-item .nav-link.active,
  .home-4 .navbar .nav-item .nav-link:hover {
    color: var(--theme-color);
  }

  .home-4 .search-form-swap{
    left: -30px;
  }
}

@media all and (max-width: 991px){
  .home-4 .navbar-collapse{
    border: 1px solid var(--border-info-color);
  }

  .home-4 .navbar-toggler-btn-icon{
    color: var(--color-dark);
  }

  .home-4 .search-wrapper{
    margin-top: 0px;
  }

  .home-4 .search-form .form-group{
    margin-bottom: 0;
  }

  .home-4 .search-wrapper .search-box{
    padding-bottom: 50px;
  }
}

