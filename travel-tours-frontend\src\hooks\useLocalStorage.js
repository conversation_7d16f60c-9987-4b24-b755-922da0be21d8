"use client";
import { useCallback } from "react";

const useLocalStorage = () => {
  // Set a value in localStorage
  const setItem = useCallback((key, value) => {
    try {
      localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error(`Error setting ${key} in localStorage:`, error);
    }
  }, []);

  // Get a value from localStorage
  const getItem = useCallback((key) => {
    try {
      const value = localStorage.getItem(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error(`Error getting ${key} from localStorage:`, error);
      return null;
    }
  }, []);

  // Remove a value from localStorage
  const removeItem = useCallback((key) => {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.error(`Error removing ${key} from localStorage:`, error);
    }
  }, []);

  return { setItem, getItem, removeItem };
};

export default useLocalStorage;
